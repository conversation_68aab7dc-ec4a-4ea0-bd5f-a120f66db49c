# 🏗️ Guia de Arquitetura - CSV Analyzer

## 📋 Visão Geral

Este documento detalha a arquitetura do CSV Analyzer, um sistema inteligente de análise de dados que combina múltiplos LLMs, agentes especializados e uma interface web intuitiva.

## 🎯 Objetivos da Arquitetura

### Principais Características
- **Modularidade**: Componentes independentes e reutilizáveis
- **Escalabilidade**: Suporte a múltiplos LLMs e agentes
- **Robustez**: Sistema de fallback e tratamento de erros
- **Usabilidade**: Interface web simples e intuitiva

### Benefícios
- **Flexibilidade**: Fácil adição de novos LLMs ou agentes
- **Confiabilidade**: Redundância através do sistema multi-LLM
- **Manutenibilidade**: Código organizado em camadas bem definidas
- **Performance**: Processamento eficiente e execução segura

## 🏗️ Arquitetura em Camadas

### 1. 🌐 Interface Layer (Camada de Interface)
**Responsabilidade**: Interação com o usuário

**Componentes**:
- **Streamlit Web Interface**: Interface principal do usuário
- **File Upload System**: Sistema de upload de arquivos
- **Results Display**: Exibição de resultados e visualizações

**Tecnologias**: Streamlit, HTML, CSS

### 2. 🤖 LLM Layer (Camada de Modelos de Linguagem)
**Responsabilidade**: Processamento de linguagem natural

**Componentes**:
- **LLM Manager**: Gerenciador central dos modelos
- **Google Gemini**: Modelo principal (gratuito, rápido)
- **OpenAI GPT**: Modelo backup (opcional, pago)
- **Fallback System**: Sistema de redundância automática

**Tecnologias**: Google Generative AI, OpenAI API, LangChain

### 3. 🔄 Orchestration Layer (Camada de Orquestração)
**Responsabilidade**: Coordenação do fluxo de trabalho

**Componentes**:
- **LangGraph Workflow**: Orquestrador principal
- **Analysis State**: Estado compartilhado (TypedDict)

**Tecnologias**: LangGraph, Python TypedDict

### 4. 🤖 Agent Layer (Camada de Agentes)
**Responsabilidade**: Processamento especializado

**Componentes**:
- **File Discovery Agent**: Descoberta de arquivos
- **File Selection Agent**: Seleção inteligente
- **Data Loading Agent**: Carregamento de dados
- **Analysis Agent**: Geração e execução de análises

**Tecnologias**: LangGraph Agents, Python

### 5. 📊 Data Layer (Camada de Dados)
**Responsabilidade**: Processamento e manipulação de dados

**Componentes**:
- **File Manager**: Gerenciamento de arquivos
- **CSV Processor**: Processamento de CSV
- **ZIP Extractor**: Extração de arquivos compactados
- **Code Executor**: Execução segura de código

**Tecnologias**: Pandas, NumPy, Python zipfile

### 6. 💾 Storage Layer (Camada de Armazenamento)
**Responsabilidade**: Persistência e configuração

**Componentes**:
- **uploads/ Directory**: Diretório de uploads
- **Temporary Files**: Arquivos temporários
- **Environment Config**: Configurações (.env)

**Tecnologias**: Sistema de arquivos, python-dotenv

## 🔄 Fluxo de Dados

### Processo Principal
1. **Upload** → Interface recebe arquivos do usuário
2. **Discovery** → Agente descobre arquivos CSV disponíveis
3. **Selection** → LLM seleciona arquivo mais relevante
4. **Loading** → Dados são carregados e validados
5. **Analysis** → LLM gera código de análise
6. **Execution** → Código é executado de forma segura
7. **Display** → Resultados são apresentados ao usuário

### Sistema de Fallback
```
Gemini (Primário) → OpenAI (Backup) → Erro
```

## 🛡️ Segurança e Robustez

### Execução Segura
- **Namespace Controlado**: Execução em ambiente restrito
- **Validação de Entrada**: Verificação de dados de entrada
- **Sanitização de Código**: Limpeza do código gerado

### Tratamento de Erros
- **Validação em Cada Etapa**: Verificação contínua
- **Mensagens Informativas**: Feedback claro ao usuário
- **Recuperação Automática**: Tentativas de recuperação

### Monitoramento
- **Logging Detalhado**: Rastreamento de operações
- **Estado Compartilhado**: Visibilidade do processo
- **Métricas de Performance**: Monitoramento de desempenho

## 🔧 Configuração e Extensibilidade

### Adicionando Novos LLMs
1. Implementar wrapper compatível com LangChain
2. Adicionar ao LLMManager
3. Configurar fallback apropriado
4. Atualizar configurações de ambiente

### Criando Novos Agentes
1. Implementar função de agente
2. Definir entrada e saída de estado
3. Adicionar ao workflow do LangGraph
4. Configurar transições condicionais

### Personalizando Interface
1. Modificar componentes Streamlit
2. Adicionar novas visualizações
3. Implementar novos tipos de upload
4. Customizar exibição de resultados

## 📊 Métricas e Performance

### Indicadores Chave
- **Tempo de Resposta**: Latência end-to-end
- **Taxa de Sucesso**: Porcentagem de análises bem-sucedidas
- **Uso de LLM**: Distribuição entre Gemini e OpenAI
- **Tamanho de Arquivos**: Capacidade de processamento

### Otimizações
- **Cache de Resultados**: Evita reprocessamento
- **Processamento Assíncrono**: Melhora responsividade
- **Validação Prévia**: Reduz erros de execução
- **Compressão de Estado**: Otimiza memória

---

**Esta arquitetura garante um sistema robusto, escalável e fácil de manter para análise inteligente de dados CSV.**
