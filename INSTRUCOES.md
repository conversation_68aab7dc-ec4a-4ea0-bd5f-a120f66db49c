# 🚀 Instruções de Uso - CSV Analyzer

## ✅ Status Atual

- ✅ Dependências instaladas
- ✅ Arquivos configurados
- ✅ **Google Gemini configurado e funcionando**
- ⚠️ OpenAI opcional (não necessário)

## 🔧 Configuração Final

### ✅ Sistema Pronto!

**Google Gemini já está configurado e funcionando!**

Não é necessário configurar nada adicional. O sistema está pronto para uso.

### 🔧 Configuração Opcional - OpenAI

Se quiser usar OpenAI como alternativa:

1. **Obter chave OpenAI**:

   - Acesse: https://platform.openai.com/api-keys
   - Faça login ou crie uma conta
   - Clique em "Create new secret key"
   - Copie a chave (começa com `sk-`)

2. **Configurar no .env**:

```env
OPENAI_API_KEY=sk-sua_chave_real_da_openai_aqui
PREFERRED_LLM=openai
```

## 🚀 Executar a Aplicação

### Opção 1: Script automático

```bash
python3 run.py
```

### Opção 2: Comando direto

```bash
streamlit run main.py
```

### Opção 3: Com configurações específicas

```bash
streamlit run main.py --server.port 8501 --server.headless true
```

## 🌐 Acessar a Interface

Após executar, acesse no navegador:

- **URL**: http://localhost:8501
- **Porta**: 8501 (padrão)

## 📊 Como Usar

### 1. Carregar Dados

- Use a barra lateral para fazer upload de arquivos CSV ou ZIP
- Múltiplos arquivos são suportados
- Formatos aceitos: `.csv`, `.zip`

### 2. Fazer Perguntas

Digite perguntas como:

- "Qual fornecedor recebeu maior montante?"
- "Mostre os top 5 produtos por vendas"
- "Qual a média de preços por categoria?"
- "Quantas linhas tem o dataset?"

### 3. Ver Resultados

- Os agentes processam automaticamente
- Código Python é gerado e executado
- Resultados são apresentados na interface

## 🛠️ Scripts Disponíveis

- **`install.py`**: Instala dependências
- **`test_setup.py`**: Verifica configuração
- **`run.py`**: Executa a aplicação
- **`generate_test_data.py`**: Gera dados de teste

## 🔍 Verificar Status

Para verificar se tudo está funcionando:

```bash
python3 test_setup.py
```

## 📁 Dados de Teste

O projeto já inclui alguns arquivos CSV de exemplo na pasta `uploads/`:

- `vendas.csv`
- `produtos_entregues.csv`
- `fornecedores.csv`
- `estoque.csv`

## 🆘 Solução de Problemas

### Erro: "OPENAI_API_KEY não configurada"

- Configure a chave no arquivo `.env`
- Reinicie a aplicação

### Erro: "Module not found"

- Execute: `python3 install.py`
- Verifique se está no ambiente virtual correto

### Erro: "Port already in use"

- Mude a porta: `streamlit run main.py --server.port 8502`
- Ou mate processos: `pkill -f streamlit`

### Interface não carrega

- Verifique se o firewall permite a porta 8501
- Tente acessar: http://127.0.0.1:8501

## 💡 Dicas

1. **Performance**: Use arquivos CSV menores para testes iniciais
2. **Perguntas**: Seja específico nas perguntas para melhores resultados
3. **Logs**: Verifique o terminal para mensagens de debug
4. **Backup**: Mantenha backup dos seus dados importantes

## 🎯 Próximos Passos

1. Configure OPENAI_API_KEY
2. Execute: `python3 run.py`
3. Acesse: http://localhost:8501
4. Teste com os dados de exemplo
5. Carregue seus próprios dados

---

**🎉 Pronto para usar! Divirta-se analisando seus dados!**
