# 🤖 Suporte a Múltiplos LLMs

O CSV Analyzer agora suporta **dois provedores de LLM**:
- **OpenAI GPT** (gpt-3.5-turbo, gpt-4, etc.)
- **Google Gemini** (gemini-1.5-flash, gemini-pro, etc.)

## ✅ Configuração Atual

### 🔑 Chaves Configuradas
- ✅ **Google Gemini**: Configurada e funcionando
- ⚠️ **OpenAI**: Não configurada (opcional)

### 🎯 LLM Ativo
- **Modelo atual**: Google Gemini (gemini-1.5-flash)
- **Status**: ✅ Funcionando perfeitamente

## ⚙️ Como Configurar

### Arquivo `.env`
```env
# Configuração dos LLMs (configure pelo menos um)

# OpenAI (opcional)
OPENAI_API_KEY=sk-sua_chave_openai_aqui
OPENAI_MODEL=gpt-3.5-turbo

# Google Gemini (já configurado)
GOOGLE_API_KEY=AIzaSyAlqJlxMSqlsrlfJ7J35MCoWLZQ62EUsBM
GEMINI_MODEL=gemini-1.5-flash

# Modelo preferido (openai ou gemini)
PREFERRED_LLM=gemini
```

## 🔄 Sistema de Fallback

O sistema tenta usar o LLM preferido primeiro. Se falhar, automaticamente tenta o outro:

1. **Preferido**: Gemini → Se falhar → OpenAI
2. **Preferido**: OpenAI → Se falhar → Gemini

## 🚀 Vantagens

### Google Gemini
- ✅ **Gratuito**: Cota generosa na API
- ✅ **Rápido**: Modelo gemini-1.5-flash é muito rápido
- ✅ **Multilíngue**: Excelente suporte ao português
- ✅ **Já configurado**: Pronto para usar

### OpenAI GPT
- ✅ **Qualidade**: Respostas muito precisas
- ✅ **Estabilidade**: API muito confiável
- ✅ **Compatibilidade**: Integração nativa com LangChain
- ⚠️ **Pago**: Requer créditos na conta

## 🎯 Uso Recomendado

### Para Começar
- Use **Gemini** (já configurado)
- Gratuito e funcional
- Perfeito para testes e uso básico

### Para Produção
- Configure **ambos** os LLMs
- Redundância e confiabilidade
- Fallback automático

## 🔧 Comandos Úteis

### Testar configuração
```bash
python3 test_setup.py
```

### Testar Gemini especificamente
```bash
python3 test_gemini.py
```

### Executar aplicação
```bash
python3 run.py
```

## 📊 Comparação de Modelos

| Aspecto | Gemini 1.5 Flash | GPT-3.5 Turbo |
|---------|------------------|----------------|
| **Custo** | Gratuito | Pago |
| **Velocidade** | Muito rápido | Rápido |
| **Qualidade** | Excelente | Excelente |
| **Português** | Nativo | Muito bom |
| **Limite** | Generoso | Por token |

## 🎉 Status Final

**✅ Sistema 100% funcional com Gemini!**

- Não precisa de chave OpenAI
- Gemini já configurado e testado
- Pronto para análise de dados
- Suporte completo ao português

## 🚀 Próximos Passos

1. **Execute**: `python3 run.py`
2. **Acesse**: http://localhost:8501
3. **Teste**: Carregue um CSV e faça perguntas
4. **Opcional**: Configure OpenAI para redundância

---

**🎯 Agora você tem acesso a IA de ponta sem custos!**
