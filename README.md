# 🤖 CSV Analyzer com LangGraph

Sistema inteligente de análise de dados CSV usando LangGraph e agentes especializados.

## ✨ Características

- **🔍 Análise Automática**: Agentes especializados analisam seus dados automaticamente
- **🌐 Interface Web**: Interface amigável com Streamlit
- **📁 Múltiplos Formatos**: Suporte a CSV e arquivos ZIP
- **🧠 IA Integrada**: Usa OpenAI GPT para análises inteligentes
- **⚡ Workflow Inteligente**: LangGraph coordena o processo de análise
- **🔒 Execução Segura**: Código de análise executado em ambiente controlado

## 🚀 Instalação Rápida

### Opção 1: Script Automático

```bash
python3 install.py
```

### Opção 2: Manual

```bash
# 1. Instalar dependências
pip install -r requirements.txt

# 2. Configurar ambiente
cp .env.example .env

# 3. Editar .env com sua chave OpenAI
nano .env

# 4. Executar aplicação
streamlit run main.py
```

## ⚙️ Configuração

### 🤖 Múltiplos LLMs Suportados

- **Google Gemini** (já configurado e funcionando!)
- **OpenAI GPT** (opcional)

### 🔑 Configuração no arquivo `.env`:

```env
# Google Gemini (já configurado)
GOOGLE_API_KEY=AIzaSyAlqJlxMSqlsrlfJ7J35MCoWLZQ62EUsBM
GEMINI_MODEL=gemini-1.5-flash

# OpenAI (opcional)
OPENAI_API_KEY=sk-sua_chave_aqui
OPENAI_MODEL=gpt-3.5-turbo

# Modelo preferido
PREFERRED_LLM=gemini
```

### 🚀 Pronto para usar!

- **Gemini já configurado** - sem necessidade de chaves adicionais
- **Acesse**: http://localhost:8501

## 📖 Como Usar

### 1. Carregue seus dados

- Arraste arquivos CSV ou ZIP para a interface
- Suporte a múltiplos arquivos
- Detecção automática de encoding

### 2. Faça perguntas inteligentes

- Use linguagem natural
- Seja específico sobre o que deseja saber
- Veja exemplos na interface

### 3. Obtenha análises automáticas

- Agentes selecionam o arquivo mais relevante
- Código de análise gerado automaticamente
- Resultados apresentados de forma clara

## 💡 Exemplos de Perguntas

### Análises Básicas

- "Quantas linhas tem o dataset?"
- "Quais são as colunas disponíveis?"
- "Mostre um resumo estatístico dos dados"

### Análises Avançadas

- "Qual fornecedor recebeu o maior montante?"
- "Qual produto teve maior volume de vendas?"
- "Qual a média de preços por categoria?"
- "Quais são os top 5 clientes por valor?"
- "Qual a tendência de vendas por mês?"

### Análises Comparativas

- "Compare vendas entre regiões"
- "Qual categoria tem maior margem?"
- "Identifique outliers nos dados"

## 🏗️ Arquitetura do Sistema

O sistema usa uma arquitetura de agentes especializados coordenados pelo LangGraph:

1. **🔍 Agente de Descoberta**: Encontra arquivos CSV disponíveis
2. **🎯 Agente de Seleção**: Escolhe o arquivo mais relevante para a pergunta
3. **📊 Agente de Carregamento**: Carrega e processa os dados com encoding automático
4. **🧮 Agente de Análise**: Gera e executa código Python para responder a pergunta

## 🛠️ Tecnologias

- **LangGraph**: Orquestração de agentes e workflows
- **LangChain**: Framework de IA e prompts
- **Streamlit**: Interface web interativa
- **Pandas**: Análise e manipulação de dados
- **OpenAI**: Modelo de linguagem GPT
- **Python**: Linguagem principal

## 📋 Requisitos

- **Python 3.8+**
- **Chave da API OpenAI**
- **Dependências**: Listadas em `requirements.txt`

## 🔧 Desenvolvimento

### Estrutura do Projeto

```
csv-analyzer-langgraph/
├── main.py              # Aplicação principal
├── install.py           # Script de instalação
├── setup.py            # Configuração avançada
├── generate_test_data.py # Gerador de dados de teste
├── requirements.txt     # Dependências
├── .env.example        # Exemplo de configuração
├── uploads/            # Diretório de uploads
└── README.md          # Este arquivo
```

### Executar em Modo Debug

```bash
export DEBUG=True
streamlit run main.py --logger.level=debug
```

## 🆘 Suporte

- **Issues**: Reporte bugs no GitHub
- **Documentação**: Veja os exemplos na interface
- **Logs**: Verifique os logs para debug

---

**Desenvolvido com ❤️ usando LangGraph e Streamlit**
