# 🤖 CSV Analyzer com LangGraph

Sistema inteligente de análise de dados CSV usando LangGraph e agentes especializados.

## ✨ Características

- **🔍 Análise Automática**: Agentes especializados analisam seus dados automaticamente
- **🌐 Interface Web**: Interface amigável com Streamlit
- **📁 Múltiplos Formatos**: Suporte a CSV e arquivos ZIP
- **🧠 IA Integrada**: Usa OpenAI GPT para análises inteligentes
- **⚡ Workflow Inteligente**: LangGraph coordena o processo de análise
- **🔒 Execução Segura**: Código de análise executado em ambiente controlado

## 🏗️ Arquitetura do Sistema

### Sistema de Componentes

O CSV Analyzer é construído com uma arquitetura modular que integra múltiplos LLMs, agentes especializados e uma interface web intuitiva:

```mermaid
graph TB
    subgraph "🌐 Interface Layer"
        UI[Streamlit Web Interface]
        UP[File Upload System]
        RES[Results Display]
    end

    subgraph "🤖 LLM Layer"
        LLM_MGR[LLM Manager]
        GEMINI[Google Gemini<br/>gemini-1.5-flash]
        OPENAI[OpenAI GPT<br/>gpt-3.5-turbo]
        FALLBACK{Fallback System}
    end

    subgraph "🔄 Orchestration Layer"
        LG[LangGraph Workflow]
        STATE[Analysis State<br/>TypedDict]
    end

    subgraph "🤖 Agent Layer"
        A1[File Discovery<br/>Agent]
        A2[File Selection<br/>Agent]
        A3[Data Loading<br/>Agent]
        A4[Analysis<br/>Agent]
    end

    subgraph "📊 Data Layer"
        FM[File Manager]
        CSV[CSV Processor]
        ZIP[ZIP Extractor]
        EXEC[Code Executor]
    end

    subgraph "💾 Storage Layer"
        UPLOADS[uploads/ Directory]
        TEMP[Temporary Files]
        ENV[Environment Config]
    end

    %% Interface connections
    UI --> UP
    UI --> RES
    UP --> FM

    %% LLM connections
    LLM_MGR --> FALLBACK
    FALLBACK -->|Primary| GEMINI
    FALLBACK -->|Secondary| OPENAI

    %% Orchestration connections
    UI --> LG
    LG --> STATE
    STATE --> A1
    STATE --> A2
    STATE --> A3
    STATE --> A4

    %% Agent connections
    A1 --> FM
    A2 --> LLM_MGR
    A3 --> CSV
    A4 --> LLM_MGR
    A4 --> EXEC

    %% Data layer connections
    FM --> UPLOADS
    FM --> ZIP
    CSV --> TEMP
    A1 --> A2
    A2 --> A3
    A3 --> A4
    A4 --> RES

    %% Configuration
    ENV --> LLM_MGR
    ENV --> FM

    %% Styling
    classDef interface fill:#e1f5fe
    classDef llm fill:#f3e5f5
    classDef orchestration fill:#e8f5e8
    classDef agent fill:#fff3e0
    classDef data fill:#fce4ec
    classDef storage fill:#f1f8e9

    class UI,UP,RES interface
    class LLM_MGR,GEMINI,OPENAI,FALLBACK llm
    class LG,STATE orchestration
    class A1,A2,A3,A4 agent
    class FM,CSV,ZIP,EXEC data
    class UPLOADS,TEMP,ENV storage
```

### Fluxo de Análise Detalhado

Este diagrama mostra o processo completo desde o upload do arquivo até a apresentação dos resultados:

```mermaid
flowchart TD
    START([👤 Usuário inicia análise]) --> UPLOAD{📁 Arquivos<br/>carregados?}

    UPLOAD -->|Não| UPLOAD_FILE[📤 Upload CSV/ZIP]
    UPLOAD_FILE --> SAVE_FILE[💾 Salvar em uploads/]
    SAVE_FILE --> UPLOAD

    UPLOAD -->|Sim| QUESTION[❓ Usuário faz pergunta]
    QUESTION --> INIT_STATE[🔄 Inicializar Estado<br/>LangGraph]

    INIT_STATE --> AGENT1[🔍 File Discovery Agent]

    %% File Discovery Agent
    AGENT1 --> SCAN[📂 Escanear diretório uploads/]
    SCAN --> FIND_CSV{CSV files<br/>encontrados?}
    FIND_CSV -->|Não| ERROR1[❌ Erro: Nenhum arquivo]
    FIND_CSV -->|Sim| LIST_FILES[📋 Listar arquivos CSV]
    LIST_FILES --> UPDATE_STATE1[📝 Atualizar estado:<br/>available_files]

    UPDATE_STATE1 --> AGENT2[🎯 File Selection Agent]

    %% File Selection Agent
    AGENT2 --> SINGLE_FILE{Apenas um<br/>arquivo?}
    SINGLE_FILE -->|Sim| SELECT_SINGLE[✅ Selecionar único arquivo]
    SINGLE_FILE -->|Não| ANALYZE_FILES[🔍 Analisar estrutura<br/>de cada arquivo]

    ANALYZE_FILES --> LLM_SELECT[🤖 LLM seleciona<br/>arquivo mais relevante]
    LLM_SELECT --> LLM_CALL1{LLM<br/>disponível?}

    LLM_CALL1 -->|Gemini OK| USE_GEMINI1[🟢 Usar Google Gemini]
    LLM_CALL1 -->|Gemini Fail| TRY_OPENAI1[🔄 Tentar OpenAI]
    TRY_OPENAI1 -->|OpenAI OK| USE_OPENAI1[🟡 Usar OpenAI GPT]
    TRY_OPENAI1 -->|OpenAI Fail| ERROR2[❌ Erro: Nenhum LLM]

    USE_GEMINI1 --> SELECT_BEST[✅ Arquivo selecionado]
    USE_OPENAI1 --> SELECT_BEST
    SELECT_SINGLE --> SELECT_BEST

    SELECT_BEST --> UPDATE_STATE2[📝 Atualizar estado:<br/>selected_file]
    UPDATE_STATE2 --> AGENT3[📊 Data Loading Agent]

    %% Data Loading Agent
    AGENT3 --> LOAD_CSV[📖 Carregar arquivo CSV]
    LOAD_CSV --> ENCODING[🔤 Detectar encoding<br/>utf-8, latin-1, cp1252]
    ENCODING --> PARSE_SUCCESS{Parsing<br/>bem-sucedido?}

    PARSE_SUCCESS -->|Não| ERROR3[❌ Erro: Falha no parsing]
    PARSE_SUCCESS -->|Sim| VALIDATE[✅ Validar DataFrame]
    VALIDATE --> UPDATE_STATE3[📝 Atualizar estado:<br/>data]

    UPDATE_STATE3 --> AGENT4[🧮 Analysis Agent]

    %% Analysis Agent
    AGENT4 --> ANALYZE_STRUCTURE[📊 Analisar estrutura<br/>colunas, tipos, amostra]
    ANALYZE_STRUCTURE --> GENERATE_PROMPT[📝 Gerar prompt<br/>para análise]

    GENERATE_PROMPT --> LLM_CALL2{LLM<br/>disponível?}
    LLM_CALL2 -->|Gemini OK| USE_GEMINI2[🟢 Gemini gera código]
    LLM_CALL2 -->|Gemini Fail| TRY_OPENAI2[🔄 Tentar OpenAI]
    TRY_OPENAI2 -->|OpenAI OK| USE_OPENAI2[🟡 OpenAI gera código]
    TRY_OPENAI2 -->|OpenAI Fail| ERROR4[❌ Erro: Nenhum LLM]

    USE_GEMINI2 --> CODE_GENERATED[📋 Código Python gerado]
    USE_OPENAI2 --> CODE_GENERATED

    CODE_GENERATED --> SAFE_EXEC[🔒 Execução segura<br/>namespace controlado]
    SAFE_EXEC --> EXEC_SUCCESS{Execução<br/>bem-sucedida?}

    EXEC_SUCCESS -->|Não| ERROR5[❌ Erro na execução]
    EXEC_SUCCESS -->|Sim| CAPTURE_OUTPUT[📤 Capturar resultado]

    CAPTURE_OUTPUT --> UPDATE_STATE4[📝 Atualizar estado:<br/>analysis_result]
    UPDATE_STATE4 --> COMPLETE[✅ Análise completa]

    %% Results Display
    COMPLETE --> DISPLAY[🖥️ Exibir resultados]
    DISPLAY --> SHOW_FILE[📄 Mostrar arquivo usado]
    SHOW_FILE --> SHOW_RESULT[📈 Mostrar análise]
    SHOW_RESULT --> SHOW_SAMPLE[👁️ Mostrar amostra dados]
    SHOW_SAMPLE --> END([🎉 Processo concluído])

    %% Error handling
    ERROR1 --> END_ERROR([❌ Fim com erro])
    ERROR2 --> END_ERROR
    ERROR3 --> END_ERROR
    ERROR4 --> END_ERROR
    ERROR5 --> END_ERROR

    %% Styling
    classDef startEnd fill:#4caf50,stroke:#2e7d32,color:#fff
    classDef agent fill:#2196f3,stroke:#1565c0,color:#fff
    classDef llm fill:#9c27b0,stroke:#6a1b9a,color:#fff
    classDef process fill:#ff9800,stroke:#ef6c00,color:#fff
    classDef decision fill:#ffc107,stroke:#f57c00,color:#000
    classDef error fill:#f44336,stroke:#c62828,color:#fff
    classDef success fill:#4caf50,stroke:#2e7d32,color:#fff

    class START,END startEnd
    class AGENT1,AGENT2,AGENT3,AGENT4 agent
    class USE_GEMINI1,USE_GEMINI2,USE_OPENAI1,USE_OPENAI2 llm
    class UPLOAD_FILE,SAVE_FILE,SCAN,ANALYZE_FILES,LOAD_CSV,ENCODING,ANALYZE_STRUCTURE,GENERATE_PROMPT,SAFE_EXEC,CAPTURE_OUTPUT,DISPLAY,SHOW_FILE,SHOW_RESULT,SHOW_SAMPLE process
    class UPLOAD,FIND_CSV,SINGLE_FILE,LLM_CALL1,LLM_CALL2,PARSE_SUCCESS,EXEC_SUCCESS decision
    class ERROR1,ERROR2,ERROR3,ERROR4,ERROR5,END_ERROR error
    class SELECT_BEST,VALIDATE,CODE_GENERATED,COMPLETE success
```

### Características Técnicas dos Diagramas

**🔄 Sistema de Fallback Multi-LLM:**

- Prioridade: Google Gemini (gratuito, rápido)
- Fallback: OpenAI GPT (backup confiável)
- Detecção automática de disponibilidade

**🛡️ Tratamento de Erros:**

- Validação em cada etapa do processo
- Recuperação automática quando possível
- Mensagens de erro informativas

**⚡ Otimizações:**

- Cache de resultados intermediários
- Execução assíncrona de agentes
- Processamento eficiente de arquivos grandes

## 🚀 Instalação Rápida

### Opção 1: Script Automático

```bash
python3 install.py
```

### Opção 2: Manual

```bash
# 1. Instalar dependências
pip install -r requirements.txt

# 2. Configurar ambiente
cp .env.example .env

# 3. Editar .env com sua chave OpenAI
nano .env

# 4. Executar aplicação
streamlit run main.py
```

## ⚙️ Configuração

### 🤖 Múltiplos LLMs Suportados

- **Google Gemini** (já configurado e funcionando!)
- **OpenAI GPT** (opcional)

### 🔑 Configuração no arquivo `.env`:

```env
# Google Gemini (já configurado)
GOOGLE_API_KEY=AIzaSyAlqJlxMSqlsrlfJ7J35MCoWLZQ62EUsBM
GEMINI_MODEL=gemini-1.5-flash

# OpenAI (opcional)
OPENAI_API_KEY=sk-sua_chave_aqui
OPENAI_MODEL=gpt-3.5-turbo

# Modelo preferido
PREFERRED_LLM=gemini
```

### 🚀 Pronto para usar!

- **Gemini já configurado** - sem necessidade de chaves adicionais
- **Acesse**: http://localhost:8501

## 📖 Como Usar

### 1. Carregue seus dados

- Arraste arquivos CSV ou ZIP para a interface
- Suporte a múltiplos arquivos
- Detecção automática de encoding

### 2. Faça perguntas inteligentes

- Use linguagem natural
- Seja específico sobre o que deseja saber
- Veja exemplos na interface

### 3. Obtenha análises automáticas

- Agentes selecionam o arquivo mais relevante
- Código de análise gerado automaticamente
- Resultados apresentados de forma clara

## 💡 Exemplos de Perguntas

### Análises Básicas

- "Quantas linhas tem o dataset?"
- "Quais são as colunas disponíveis?"
- "Mostre um resumo estatístico dos dados"

### Análises Avançadas

- "Qual fornecedor recebeu o maior montante?"
- "Qual produto teve maior volume de vendas?"
- "Qual a média de preços por categoria?"
- "Quais são os top 5 clientes por valor?"
- "Qual a tendência de vendas por mês?"

### Análises Comparativas

- "Compare vendas entre regiões"
- "Qual categoria tem maior margem?"
- "Identifique outliers nos dados"

## 🏗️ Arquitetura do Sistema

O sistema usa uma arquitetura de agentes especializados coordenados pelo LangGraph:

1. **🔍 Agente de Descoberta**: Encontra arquivos CSV disponíveis
2. **🎯 Agente de Seleção**: Escolhe o arquivo mais relevante para a pergunta
3. **📊 Agente de Carregamento**: Carrega e processa os dados com encoding automático
4. **🧮 Agente de Análise**: Gera e executa código Python para responder a pergunta

## 🛠️ Tecnologias

- **LangGraph**: Orquestração de agentes e workflows
- **LangChain**: Framework de IA e prompts
- **Streamlit**: Interface web interativa
- **Pandas**: Análise e manipulação de dados
- **OpenAI**: Modelo de linguagem GPT
- **Python**: Linguagem principal

## 📋 Requisitos

- **Python 3.8+**
- **Chave da API OpenAI**
- **Dependências**: Listadas em `requirements.txt`

## 🔧 Desenvolvimento

### Estrutura do Projeto

```
csv-analyzer-langgraph/
├── main.py              # Aplicação principal
├── install.py           # Script de instalação
├── setup.py            # Configuração avançada
├── generate_test_data.py # Gerador de dados de teste
├── requirements.txt     # Dependências
├── .env.example        # Exemplo de configuração
├── uploads/            # Diretório de uploads
└── README.md          # Este arquivo
```

### Executar em Modo Debug

```bash
export DEBUG=True
streamlit run main.py --logger.level=debug
```

## 🆘 Suporte

- **Issues**: Reporte bugs no GitHub
- **Documentação**: Veja os exemplos na interface
- **Logs**: Verifique os logs para debug

---

**Desenvolvido com ❤️ usando LangGraph e Streamlit**
