import streamlit as st

import os
import pandas as pd
import zipfile
import tempfile
import shutil
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from pathlib import Path
from langgraph.graph import StateGraph, END
from langchain_core.messages import HumanMessage, AIMessage
from langchain_core.prompts import ChatPromptTemplate
import json
import logging
from dotenv import load_dotenv
import sys
import io

# Carrega variáveis de ambiente
load_dotenv()

# Configuração de logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LLMManager:
    """Gerenciador de múltiplos modelos de LLM"""

    def __init__(self):
        self.llm = None
        self.provider = None
        self._initialize_llm()

    def _initialize_llm(self):
        """Inicializa o LLM baseado nas configurações disponíveis"""
        preferred_llm = os.getenv('PREFERRED_LLM', 'gemini').lower()

        # Tenta inicializar o LLM preferido primeiro
        if preferred_llm == 'gemini' and self._try_gemini():
            return
        elif preferred_llm == 'openai' and self._try_openai():
            return

        # Se o preferido falhar, tenta o outro
        if preferred_llm == 'gemini':
            if not self._try_openai():
                raise ValueError("Nenhum LLM configurado. Configure OpenAI ou Gemini no .env")
        else:
            if not self._try_gemini():
                raise ValueError("Nenhum LLM configurado. Configure OpenAI ou Gemini no .env")

    def _try_openai(self):
        """Tenta inicializar OpenAI"""
        try:
            from langchain_openai import ChatOpenAI

            api_key = os.getenv('OPENAI_API_KEY')
            model = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')

            if api_key and api_key != 'sk-sua_chave_aqui':
                self.llm = ChatOpenAI(
                    model=model,
                    temperature=0,
                    openai_api_key=api_key
                )
                self.provider = 'openai'
                logger.info(f"✅ OpenAI inicializado: {model}")
                return True
        except Exception as e:
            logger.warning(f"⚠️ Falha ao inicializar OpenAI: {e}")
        return False

    def _try_gemini(self):
        """Tenta inicializar Gemini"""
        try:
            import google.generativeai as genai

            api_key = os.getenv('GOOGLE_API_KEY')
            model = os.getenv('GEMINI_MODEL', 'gemini-1.5-flash')

            if api_key and api_key != 'sua_chave_gemini_aqui':
                genai.configure(api_key=api_key)
                self.llm = GeminiWrapper(model)
                self.provider = 'gemini'
                logger.info(f"✅ Gemini inicializado: {model}")
                return True
        except Exception as e:
            logger.warning(f"⚠️ Falha ao inicializar Gemini: {e}")
        return False

    def invoke(self, prompt):
        """Invoca o LLM com o prompt"""
        if self.llm is None:
            raise ValueError("Nenhum LLM disponível")

        if self.provider == 'gemini':
            return self.llm.invoke(prompt)
        else:
            return self.llm.invoke(prompt)

class GeminiWrapper:
    """Wrapper para compatibilizar Gemini com interface LangChain"""

    def __init__(self, model_name='gemini-1.5-flash'):
        import google.generativeai as genai
        self.model = genai.GenerativeModel(model_name)

    def invoke(self, prompt):
        """Invoca o modelo Gemini"""
        try:
            # Se for um prompt template formatado
            if hasattr(prompt, 'format'):
                text = prompt
            elif isinstance(prompt, str):
                text = prompt
            else:
                text = str(prompt)

            response = self.model.generate_content(text)

            # Retorna objeto compatível com LangChain
            return GeminiResponse(response.text)
        except Exception as e:
            logger.error(f"Erro no Gemini: {e}")
            return GeminiResponse(f"Erro: {str(e)}")

class GeminiResponse:
    """Classe para compatibilizar resposta do Gemini com LangChain"""

    def __init__(self, content):
        self.content = content

from typing_extensions import TypedDict

class AnalysisState(TypedDict):
    """Estado compartilhado entre os agentes"""
    user_question: str
    available_files: List[str]
    selected_file: str
    data: Optional[pd.DataFrame]
    analysis_result: str
    error: str
    step: str

class FileManager:
    """Gerenciador de arquivos CSV"""
    
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = Path(upload_dir)
        self.upload_dir.mkdir(exist_ok=True)
        self.temp_dir = None
    
    def extract_zip(self, zip_path: str) -> List[str]:
        """Extrai arquivos ZIP e retorna lista de CSVs"""
        try:
            self.temp_dir = tempfile.mkdtemp()
            csv_files = []
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.temp_dir)
            
            # Busca recursivamente por arquivos CSV
            for root, dirs, files in os.walk(self.temp_dir):
                for file in files:
                    if file.lower().endswith('.csv'):
                        csv_files.append(os.path.join(root, file))
            
            return csv_files
        except Exception as e:
            logger.error(f"Erro ao extrair ZIP: {str(e)}")
            return []
    
    def get_csv_files(self) -> List[str]:
        """Retorna lista de arquivos CSV disponíveis"""
        csv_files = []
        
        # Arquivos CSV diretos
        for file in self.upload_dir.glob("*.csv"):
            csv_files.append(str(file))
        
        # Arquivos ZIP
        for zip_file in self.upload_dir.glob("*.zip"):
            csv_files.extend(self.extract_zip(str(zip_file)))
        
        return csv_files
    
    def load_csv(self, file_path: str) -> pd.DataFrame:
        """Carrega arquivo CSV"""
        try:
            # Tenta diferentes encodings
            encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    logger.info(f"Arquivo carregado com encoding {encoding}")
                    return df
                except UnicodeDecodeError:
                    continue
            
            # Se nenhum encoding funcionar, usa error handling
            df = pd.read_csv(file_path, encoding='utf-8', errors='ignore')
            return df
            
        except Exception as e:
            logger.error(f"Erro ao carregar CSV: {str(e)}")
            raise
    
    def cleanup(self):
        """Limpa arquivos temporários"""
        if self.temp_dir and os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

class CSVAnalyzer:
    """Analisador de dados CSV"""
    
    def __init__(self):
        self.file_manager = FileManager()
        # Usa o gerenciador de LLM que suporta múltiplos provedores
        self.llm_manager = LLMManager()
        self.llm = self.llm_manager.llm
    
    def analyze_data_structure(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analisa a estrutura dos dados"""
        analysis = {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': df.dtypes.to_dict(),
            'null_counts': df.isnull().sum().to_dict(),
            'sample_data': df.head(3).to_dict('records')
        }
        return analysis
    
    def select_best_file(self, files: List[str], question: str) -> str:
        """Seleciona o melhor arquivo baseado na pergunta"""
        if not files:
            return ""
        
        if len(files) == 1:
            return files[0]
        
        # Analisa qual arquivo é mais relevante
        file_info = []
        for file in files:
            try:
                df = self.file_manager.load_csv(file)
                info = {
                    'file': file,
                    'columns': list(df.columns),
                    'rows': len(df)
                }
                file_info.append(info)
            except:
                continue
        
        # Usa LLM para selecionar o melhor arquivo
        prompt = ChatPromptTemplate.from_template("""
        Baseado na pergunta do usuário e nas informações dos arquivos disponíveis,
        selecione o arquivo mais apropriado para responder a pergunta.
        
        Pergunta: {question}
        
        Arquivos disponíveis:
        {files_info}
        
        Responda apenas com o caminho do arquivo selecionado.
        """)
        
        response = self.llm.invoke(
            prompt.format(
                question=question,
                files_info=json.dumps(file_info, indent=2)
            )
        )
        
        # Extrai o caminho do arquivo da resposta
        for file in files:
            if file in response.content:
                return file
        
        return files[0]  # Fallback para o primeiro arquivo
    
    def generate_analysis_query(self, df: pd.DataFrame, question: str) -> str:
        """Gera código Python para análise baseado na pergunta"""
        data_info = self.analyze_data_structure(df)
        
        prompt = ChatPromptTemplate.from_template("""
        Você é um especialista em análise de dados com pandas. 
        Baseado na pergunta do usuário e na estrutura dos dados,
        gere código Python que responda à pergunta.
        
        Pergunta: {question}
        
        Estrutura dos dados:
        - Colunas: {columns}
        - Tipos de dados: {dtypes}
        - Formato: {shape}
        - Amostra: {sample}
        
        Regras:
        1. Use apenas pandas e numpy
        2. Assuma que o DataFrame se chama 'df'
        3. O código deve retornar o resultado da análise
        4. Seja específico e preciso
        5. Trate valores nulos se necessário
        
        Exemplo de resposta:
        ```python
        # Análise solicitada
        resultado = df.groupby('coluna')['valor'].sum().idxmax()
        print(f"Resultado: {{resultado}}")
        ```
        
        Responda apenas com o código Python, sem explicações adicionais.
        """)
        
        response = self.llm.invoke(
            prompt.format(
                question=question,
                columns=data_info['columns'],
                dtypes=str(data_info['dtypes']),
                shape=data_info['shape'],
                sample=str(data_info['sample_data'])
            )
        )
        
        return response.content.strip()
    
    def execute_analysis(self, df: pd.DataFrame, code: str) -> str:
        """Executa o código de análise de forma segura"""
        try:
            # Extrai apenas o código Python
            if '```python' in code:
                code = code.split('```python')[1].split('```')[0]
            elif '```' in code:
                code = code.split('```')[1]

            # Remove linhas vazias e comentários desnecessários
            code_lines = [line.strip() for line in code.split('\n') if line.strip() and not line.strip().startswith('#')]
            code = '\n'.join(code_lines)

            # Captura output
            old_stdout = sys.stdout
            sys.stdout = captured_output = io.StringIO()

            # Namespace seguro para execução
            namespace = {
                'df': df,
                'pd': pd,
                'np': __import__('numpy'),
                'print': print,
                'len': len,
                'str': str,
                'int': int,
                'float': float,
                'sum': sum,
                'max': max,
                'min': min,
                'round': round
            }

            try:
                # Executa o código
                exec(code, namespace)
                result = captured_output.getvalue()

                # Se não há output, tenta capturar o último valor
                if not result.strip():
                    # Tenta executar novamente e capturar resultado
                    lines = code.split('\n')
                    if lines:
                        last_line = lines[-1]
                        if not last_line.startswith('print'):
                            # Adiciona print ao último statement
                            modified_code = '\n'.join(lines[:-1]) + f'\nprint({last_line})'
                            exec(modified_code, namespace)
                            result = captured_output.getvalue()

            finally:
                sys.stdout = old_stdout

            return result.strip() if result.strip() else "Análise executada com sucesso"

        except Exception as e:
            return f"Erro na execução: {str(e)}"

# Definição dos agentes
def file_discovery_agent(state: AnalysisState) -> AnalysisState:
    """Agente responsável por descobrir arquivos CSV"""
    logger.info("Executando agente de descoberta de arquivos")

    analyzer = CSVAnalyzer()
    files = analyzer.file_manager.get_csv_files()

    # Atualiza o estado
    new_state = state.copy()
    new_state["available_files"] = files
    new_state["step"] = "file_selection"

    if not files:
        new_state["error"] = "Nenhum arquivo CSV encontrado"
        new_state["step"] = "error"

    return new_state

def file_selection_agent(state: AnalysisState) -> AnalysisState:
    """Agente responsável por selecionar o arquivo mais apropriado"""
    logger.info("Executando agente de seleção de arquivo")

    new_state = state.copy()

    if not state.get("available_files"):
        new_state["error"] = "Nenhum arquivo disponível"
        new_state["step"] = "error"
        return new_state

    analyzer = CSVAnalyzer()
    selected_file = analyzer.select_best_file(state["available_files"], state["user_question"])

    new_state["selected_file"] = selected_file
    new_state["step"] = "data_loading"

    return new_state

def data_loading_agent(state: AnalysisState) -> AnalysisState:
    """Agente responsável por carregar os dados"""
    logger.info("Executando agente de carregamento de dados")

    new_state = state.copy()

    if not state.get("selected_file"):
        new_state["error"] = "Nenhum arquivo selecionado"
        new_state["step"] = "error"
        return new_state

    try:
        analyzer = CSVAnalyzer()
        df = analyzer.file_manager.load_csv(state["selected_file"])
        new_state["data"] = df
        new_state["step"] = "analysis"

        logger.info(f"Dados carregados: {df.shape[0]} linhas, {df.shape[1]} colunas")

    except Exception as e:
        new_state["error"] = f"Erro ao carregar dados: {str(e)}"
        new_state["step"] = "error"

    return new_state

def analysis_agent(state: AnalysisState) -> AnalysisState:
    """Agente responsável pela análise dos dados"""
    logger.info("Executando agente de análise")

    new_state = state.copy()

    if state.get("data") is None:
        new_state["error"] = "Dados não carregados"
        new_state["step"] = "error"
        return new_state

    try:
        analyzer = CSVAnalyzer()

        # Gera código de análise
        analysis_code = analyzer.generate_analysis_query(state["data"], state["user_question"])

        # Executa análise
        result = analyzer.execute_analysis(state["data"], analysis_code)

        new_state["analysis_result"] = result
        new_state["step"] = "complete"

        logger.info("Análise concluída com sucesso")

    except Exception as e:
        new_state["error"] = f"Erro na análise: {str(e)}"
        new_state["step"] = "error"

    return new_state

def should_continue(state: AnalysisState) -> str:
    """Determina o próximo passo baseado no estado atual"""
    step = state.get("step", "start")

    if step == "error":
        return END
    elif step == "complete":
        return END
    elif step == "file_selection":
        return "file_selection"
    elif step == "data_loading":
        return "data_loading"
    elif step == "analysis":
        return "analysis"
    else:
        return "file_discovery"

# Construção do grafo
def create_analysis_graph():
    """Cria o grafo de análise"""
    workflow = StateGraph(AnalysisState)
    
    # Adiciona nós
    workflow.add_node("file_discovery", file_discovery_agent)
    workflow.add_node("file_selection", file_selection_agent)
    workflow.add_node("data_loading", data_loading_agent)
    workflow.add_node("analysis", analysis_agent)
    
    # Define ponto de entrada
    workflow.set_entry_point("file_discovery")
    
    # Adiciona transições condicionais
    workflow.add_conditional_edges(
        "file_discovery",
        should_continue,
        {
            "file_selection": "file_selection",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "file_selection",
        should_continue,
        {
            "data_loading": "data_loading",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "data_loading",
        should_continue,
        {
            "analysis": "analysis",
            END: END
        }
    )
    
    workflow.add_conditional_edges(
        "analysis",
        should_continue,
        {
            END: END
        }
    )
    
    return workflow.compile()

# Interface Streamlit
def main():
    # Configuração da página - DEVE SER A PRIMEIRA COISA
    st.set_page_config(
        page_title="Analisador CSV com LangGraph",
        page_icon="📊",
        layout="wide"
    )

    # Verifica se pelo menos uma chave de LLM está configurada
    openai_key = os.getenv('OPENAI_API_KEY')
    gemini_key = os.getenv('GOOGLE_API_KEY')

    openai_ok = openai_key and openai_key != 'sk-sua_chave_aqui'
    gemini_ok = gemini_key and gemini_key != 'sua_chave_gemini_aqui'

    if not (openai_ok or gemini_ok):
        st.error("⚠️ Configure pelo menos uma chave de LLM no arquivo .env")
        st.markdown("""
        **Opções disponíveis:**

        **OpenAI:**
        1. Obtenha uma chave em: https://platform.openai.com/api-keys
        2. Configure no .env: `OPENAI_API_KEY=sk-sua_chave_aqui`

        **Google Gemini:**
        1. Obtenha uma chave em: https://makersuite.google.com/app/apikey
        2. Configure no .env: `GOOGLE_API_KEY=sua_chave_aqui`

        **Como configurar:**
        1. Edite o arquivo `.env`
        2. Adicione pelo menos uma das chaves acima
        3. Reinicie a aplicação
        """)
        st.stop()

    # Mostra qual LLM está sendo usado
    preferred = os.getenv('PREFERRED_LLM', 'gemini')
    if openai_ok and preferred == 'openai':
        st.sidebar.success("🤖 Usando: OpenAI GPT")
    elif gemini_ok:
        st.sidebar.success("🤖 Usando: Google Gemini")
    elif openai_ok:
        st.sidebar.success("🤖 Usando: OpenAI GPT")

    st.title("🤖 Analisador CSV com LangGraph")
    st.markdown("Faça perguntas sobre seus dados CSV e deixe os agentes analisarem para você!")
    
    # Sidebar para upload de arquivos
    with st.sidebar:
        st.header("📁 Carregar Arquivos")
        
        uploaded_files = st.file_uploader(
            "Carregue seus arquivos CSV ou ZIP",
            type=['csv', 'zip'],
            accept_multiple_files=True
        )
        
        if uploaded_files:
            upload_dir = Path("uploads")
            upload_dir.mkdir(exist_ok=True)
            
            for file in uploaded_files:
                file_path = upload_dir / file.name
                with open(file_path, "wb") as f:
                    f.write(file.getbuffer())
            
            st.success(f"✅ {len(uploaded_files)} arquivo(s) carregado(s)")
    
    # Área principal
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.header("💬 Faça sua pergunta")
        
        # Exemplos de perguntas
        with st.expander("💡 Exemplos de perguntas"):
            st.write("""
            - Qual é o fornecedor que teve maior montante recebido?
            - Qual item teve maior volume entregue em quantidade?
            - Qual o total de vendas por categoria?
            - Quais são os top 5 clientes por valor?
            - Qual a média de preços por produto?
            - Quantos pedidos foram feitos por mês?
            """)
        
        question = st.text_area(
            "Digite sua pergunta sobre os dados:",
            placeholder="Ex: Qual fornecedor recebeu o maior montante?",
            height=100
        )
        
        analyze_button = st.button("🔍 Analisar", type="primary")
    
    with col2:
        st.header("📊 Status da Análise")
        status_placeholder = st.empty()
    
    # Processamento
    if analyze_button and question:
        if not question.strip():
            st.error("Por favor, digite uma pergunta!")
            return
        
        # Cria o grafo de análise
        graph = create_analysis_graph()
        
        # Estado inicial
        initial_state: AnalysisState = {
            "user_question": question,
            "available_files": [],
            "selected_file": "",
            "data": None,
            "analysis_result": "",
            "error": "",
            "step": "start"
        }
        
        # Executa o grafo
        with st.spinner("🤖 Agentes trabalhando..."):
            status_placeholder.info("🔍 Descobrindo arquivos...")
            
            try:
                # Executa o workflow
                final_state = graph.invoke(initial_state)
                
                # Mostra resultados
                if final_state.get("step") == "error":
                    st.error(f"❌ Erro: {final_state.get('error', 'Erro desconhecido')}")
                else:
                    st.success("✅ Análise concluída!")

                    # Informações do arquivo
                    with st.expander("📄 Arquivo Analisado"):
                        selected_file = final_state.get("selected_file", "")
                        if selected_file:
                            st.write(f"**Arquivo:** {Path(selected_file).name}")

                        data = final_state.get("data")
                        if data is not None:
                            st.write(f"**Linhas:** {data.shape[0]}")
                            st.write(f"**Colunas:** {data.shape[1]}")
                            st.write("**Colunas disponíveis:**")
                            st.write(list(data.columns))

                    # Resultado da análise
                    st.header("📈 Resultado da Análise")
                    analysis_result = final_state.get("analysis_result", "Nenhum resultado disponível")
                    st.write(analysis_result)

                    # Mostra amostra dos dados
                    data = final_state.get("data")
                    if data is not None:
                        with st.expander("👁️ Amostra dos Dados"):
                            st.dataframe(data.head(10))
                
                status_placeholder.success("✅ Concluído!")
                
            except Exception as e:
                st.error(f"❌ Erro inesperado: {str(e)}")
                logger.error(f"Erro na execução: {str(e)}")
    
    # Instruções
    with st.expander("ℹ️ Como usar"):
        st.markdown("""
        1. **Carregue seus arquivos** CSV ou ZIP na barra lateral
        2. **Digite sua pergunta** sobre os dados
        3. **Clique em Analisar** e deixe os agentes trabalharem
        4. **Veja o resultado** da análise automática
        
        **Tipos de perguntas suportadas:**
        - Agregações (somas, médias, máximos, mínimos)
        - Filtragens e ordenações
        - Contagens e estatísticas
        - Comparações entre grupos
        - Rankings e top N
        """)

if __name__ == "__main__":
    main()