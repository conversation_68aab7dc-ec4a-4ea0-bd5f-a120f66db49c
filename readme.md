# Sistema de Análise CSV com LangGraph

Este projeto implementa um sistema de análise de dados CSV usando LangGraph e múltiplos agentes especializados. O sistema permite que usuários façam perguntas em linguagem natural sobre dados em arquivos CSV e recebam respostas automatizadas.

## 🏗️ Arquitetura

O sistema utiliza **LangGraph** para orquestrar 4 agentes especializados:

1. **FileDiscoveryAgent**: Descobre e lista arquivos CSV disponíveis
2. **FileSelectionAgent**: Seleciona o arquivo mais relevante para a pergunta
3. **DataLoadingAgent**: Carrega e valida os dados do CSV
4. **AnalysisAgent**: Executa a análise e gera a resposta

## 🚀 Como Executar

### 1. Instalação

```bash
# Clone o repositório
git clone [seu-repositorio]
cd csv-analyzer-langgraph

# Instale as dependências
pip install -r requirements.txt
```

### 2. Configuração

```bash
# Copie o arquivo de exemplo
cp .env.example .env

# Configure sua chave da OpenAI no arquivo .env
OPENAI_API_KEY=sk-sua_chave_aqui
```

### 3. Execução

```bash
# Execute a aplicação Streamlit
streamlit run main.py
```

A aplicação estará disponível em `http://localhost:8501`

## 📊 Como Usar

1. **Carregue seus arquivos**: Upload de arquivos CSV ou ZIP na barra lateral
2. **Faça sua pergunta**: Digite uma pergunta sobre os dados
3. **Analise**: Clique em "Analisar" e veja os agentes trabalharem
4. **Resultados**: Visualize a resposta automática

## 🤖 Agentes e Fluxo

```mermaid
graph TD
    A[Usuário faz pergunta] --> B[FileDiscoveryAgent]
    B --> C[FileSelectionAgent]
    C --> D[DataLoadingAgent]
    D --> E[AnalysisAgent]
    E --> F[Resposta Final]

    B --> G[Erro: Sem arquivos]
    C --> H[Erro: Seleção falhou]
    D --> I[Erro: Carregamento falhou]
    E --> J[Erro: Análise falhou]
```

### Detalhes dos Agentes:

**🔍 FileDiscoveryAgent**

- Busca arquivos CSV na pasta uploads
- Extrai arquivos ZIP automaticamente
- Lista todos os CSVs disponíveis

**📋 FileSelectionAgent**

- Usa LLM para selecionar arquivo mais relevante
- Analisa colunas e estrutura dos dados
- Considera a pergunta do usuário

**📥 DataLoadingAgent**

- Carrega CSV com múltiplos encodings
- Valida estrutura dos dados
- Trata erros de leitura

**🧠 AnalysisAgent**

- Gera código Python automaticamente
- Executa análise com pandas
- Retorna resultado formatado

## 📋 Exemplos de Perguntas

- "Qual é o fornecedor que teve maior montante recebido?"
- "Qual item teve maior volume entregue em quantidade?"
- "Qual o total de vendas por categoria?"
- "Quais são os top 5 clientes por valor?"
- "Qual a média de preços por produto?"
- "Quantos pedidos foram feitos por mês?"

## 🔧 Recursos Técnicos

- **Framework**: LangGraph para orquestração de agentes
- **LLM**: OpenAI GPT-3.5-turbo
- **Interface**: Streamlit
- **Processamento**: Pandas + Numpy
- **Segurança**: Execução de código em namespace controlado

## 📁 Estrutura do Projeto

```
csv-analyzer-langgraph/
├── main.py              # Aplicação principal
├── requirements.txt     # Dependências
├── .env.example        # Exemplo de configuração
├── README.md           # Este arquivo
├── uploads/            # Pasta para arquivos CSV
└── temp/              # Pasta temporária
```

## 🛡️ Segurança

- Execução de código Python em namespace restrito
- Validação de tipos de arquivo
- Limpeza automática de arquivos temporários
- Tratamento de erros robusto

## 📈 Melhorias Futuras

- [ ] Suporte a mais formatos (Excel, JSON)
- [ ] Cache de análises
- [ ] Visualizações automáticas
- [ ] Histórico de perguntas
- [ ] API REST
- [ ] Deploy em cloud

## 🤝 Contribuição

1. Fork o projeto
2. Crie sua feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para detalhes.

## 👥 Autores

- Seu Nome - [Seu GitHub](https://github.com/seuusuario)

## 🙏 Agradecimentos

- LangChain/LangGraph pela framework de agentes
- Streamlit pela interface web
- OpenAI pela API de LLM
