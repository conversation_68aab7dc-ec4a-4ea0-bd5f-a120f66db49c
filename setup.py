#!/usr/bin/env python3
"""
Script de configuração para o Sistema de Análise CSV com LangGraph
"""

import os
import sys
import subprocess
from pathlib import Path
import shutil

def check_python_version():
    """Verifica se a versão do Python é compatível"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário. Versão atual:", sys.version)
        sys.exit(1)
    print("✅ Python version:", sys.version.split()[0])

def install_requirements():
    """Instala as dependências"""
    print("📦 Instalando dependências...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependências instaladas com sucesso!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        sys.exit(1)

def setup_directories():
    """Cria diretórios necessários"""
    directories = ["uploads", "temp", "logs"]
    
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"📁 Diretório criado: {dir_name}/")

def setup_env_file():
    """Configura arquivo .env"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("📄 Arquivo .env criado a partir do .env.example")
        print("⚠️  IMPORTANTE: Configure sua OPENAI_API_KEY no arquivo .env")
    elif env_file.exists():
        print("✅ Arquivo .env já existe")
    else:
        # Cria .env básico
        with open(env_file, 'w') as f:
            f.write("# Configuração dos LLMs (configure pelo menos um)\n")
            f.write("# OpenAI\n")
            f.write("OPENAI_API_KEY=sk-sua_chave_aqui\n")
            f.write("OPENAI_MODEL=gpt-3.5-turbo\n\n")
            f.write("# Google Gemini\n")
            f.write("GOOGLE_API_KEY=sua_chave_gemini_aqui\n")
            f.write("GEMINI_MODEL=gemini-1.5-flash\n\n")
            f.write("# Modelo preferido (openai ou gemini)\n")
            f.write("PREFERRED_LLM=gemini\n\n")
            f.write("UPLOAD_DIR=uploads\n")
            f.write("TEMP_DIR=temp\n")
        print("📄 Arquivo .env criado")
        print("⚠️  IMPORTANTE: Configure sua OPENAI_API_KEY no arquivo .env")

def generate_test_data():
    """Pergunta se deve gerar dados de teste"""
    response = input("❓ Deseja gerar dados de teste? (s/n): ").lower().strip()
    
    if response in ['s', 'sim', 'y', 'yes']:
        print("🔄 Gerando dados de teste...")
        try:
            from generate_test_data import main as generate_data
            generate_data()
            print("✅ Dados de teste gerados!")
        except ImportError:
            print("❌ Erro: arquivo generate_test_data.py não encontrado")
        except Exception as e:
            print(f"❌ Erro ao gerar dados de teste: {e}")

def check_llm_keys():
    """Verifica se pelo menos uma chave de LLM está configurada"""
    from dotenv import load_dotenv

    load_dotenv()

    openai_key = os.getenv('OPENAI_API_KEY')
    gemini_key = os.getenv('GOOGLE_API_KEY')

    openai_ok = openai_key and openai_key != 'sk-sua_chave_aqui'
    gemini_ok = gemini_key and gemini_key != 'sua_chave_gemini_aqui'

    if openai_ok:
        print("✅ OPENAI_API_KEY configurada")
    if gemini_ok:
        print("✅ GOOGLE_API_KEY configurada")

    if openai_ok or gemini_ok:
        return True
    else:
        print("⚠️  Nenhuma chave de LLM configurada!")
        print("   Configure OpenAI ou Gemini no arquivo .env")
        return False

def run_application():
    """Pergunta se deve executar a aplicação"""
    if not check_llm_keys():
        print("❌ Configure pelo menos uma chave de LLM antes de executar")
        return
    
    response = input("🚀 Deseja executar a aplicação agora? (s/n): ").lower().strip()
    
    if response in ['s', 'sim', 'y', 'yes']:
        print("🌟 Iniciando aplicação Streamlit...")
        try:
            subprocess.run([sys.executable, "-m", "streamlit", "run", "main.py"])
        except KeyboardInterrupt:
            print("\n👋 Aplicação encerrada pelo usuário")
        except Exception as e:
            print(f"❌ Erro ao executar aplicação: {e}")

def main():
    """Função principal de configuração"""
    print("🔧 Configurando Sistema de Análise CSV com LangGraph")
    print("=" * 50)
    
    # Verificações iniciais
    check_python_version()
    
    # Configuração
    setup_directories()
    setup_env_file()
    install_requirements()
    
    # Dados de teste
    generate_test_data()
    
    print("\n" + "=" * 50)
    print("✅ Configuração concluída!")
    print("\n📋 Próximos passos:")
    print("1. Configure sua OPENAI_API_KEY no arquivo .env")
    print("2. Execute: streamlit run main.py")
    print("3. Acesse: http://localhost:8501")
    print("\n💡 Dicas:")
    print("- Carregue arquivos CSV na interface")
    print("- Use as perguntas de exemplo para testar")
    print("- Veja os logs para debug")
    
    # Execução opcional
    print("\n" + "=" * 50)
    run_application()

if __name__ == "__main__":
    main()