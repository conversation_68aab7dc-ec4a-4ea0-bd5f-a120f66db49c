#!/usr/bin/env python3
"""
Teste final completo do sistema
"""

import os
import sys
from dotenv import load_dotenv

def test_imports():
    """Testa todos os imports necessários"""
    print("🧪 Testando imports...")
    
    try:
        import streamlit
        print("✅ Streamlit")
    except ImportError as e:
        print(f"❌ Streamlit: {e}")
        return False
    
    try:
        import pandas
        print("✅ Pandas")
    except ImportError as e:
        print(f"❌ Pandas: {e}")
        return False
    
    try:
        import langgraph
        print("✅ LangGraph")
    except ImportError as e:
        print(f"❌ LangGraph: {e}")
        return False
    
    try:
        import google.generativeai
        print("✅ Google Generative AI")
    except ImportError as e:
        print(f"❌ Google Generative AI: {e}")
        return False
    
    return True

def test_llm_config():
    """Testa configuração dos LLMs"""
    print("\n🤖 Testando configuração dos LLMs...")
    
    load_dotenv()
    
    openai_key = os.getenv('OPENAI_API_KEY')
    gemini_key = os.getenv('GOOGLE_API_KEY')
    
    openai_ok = openai_key and openai_key != 'sk-sua_chave_aqui'
    gemini_ok = gemini_key and gemini_key != 'sua_chave_gemini_aqui'
    
    if openai_ok:
        print("✅ OpenAI configurado")
    else:
        print("⚠️ OpenAI não configurado")
    
    if gemini_ok:
        print("✅ Gemini configurado")
    else:
        print("❌ Gemini não configurado")
    
    return openai_ok or gemini_ok

def test_gemini_connection():
    """Testa conexão com Gemini"""
    print("\n🔗 Testando conexão com Gemini...")
    
    try:
        import google.generativeai as genai
        
        load_dotenv()
        api_key = os.getenv('GOOGLE_API_KEY')
        
        if not api_key:
            print("❌ Chave do Gemini não encontrada")
            return False
        
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')
        
        response = model.generate_content("Responda apenas: 'Gemini funcionando!'")
        print(f"✅ Gemini resposta: {response.text.strip()}")
        return True
        
    except Exception as e:
        print(f"❌ Erro no Gemini: {e}")
        return False

def test_main_import():
    """Testa se o main.py pode ser importado"""
    print("\n📄 Testando importação do main.py...")
    
    try:
        # Simula execução do Streamlit sem interface
        os.environ['STREAMLIT_SERVER_HEADLESS'] = 'true'
        
        # Tenta importar o módulo principal
        import main
        print("✅ main.py importado com sucesso")
        return True
        
    except Exception as e:
        print(f"❌ Erro ao importar main.py: {e}")
        return False

def main():
    print("🔍 TESTE FINAL COMPLETO - CSV Analyzer")
    print("=" * 50)
    
    # Executa todos os testes
    imports_ok = test_imports()
    llm_ok = test_llm_config()
    gemini_ok = test_gemini_connection()
    main_ok = test_main_import()
    
    print("\n" + "=" * 50)
    print("📊 RESULTADO FINAL:")
    
    if imports_ok and llm_ok and gemini_ok and main_ok:
        print("🎉 TODOS OS TESTES PASSARAM!")
        print("✅ Sistema 100% funcional")
        print("\n🚀 Para executar:")
        print("   source venv/bin/activate")
        print("   streamlit run main.py")
        print("   Acesse: http://localhost:8501")
    else:
        print("❌ Alguns testes falharam")
        if not imports_ok:
            print("   - Execute: python3 install.py")
        if not llm_ok:
            print("   - Configure chaves no .env")
        if not gemini_ok:
            print("   - Verifique chave do Gemini")
        if not main_ok:
            print("   - Verifique código do main.py")

if __name__ == "__main__":
    main()
