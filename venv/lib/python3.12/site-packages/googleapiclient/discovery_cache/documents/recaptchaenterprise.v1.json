{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://recaptchaenterprise.googleapis.com/", "batchPath": "batch", "canonicalName": "Recaptcha Enterprise", "description": "Help protect your website from fraudulent activity, spam, and abuse without creating friction.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/recaptcha-enterprise/", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "recaptchaenterprise:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://recaptchaenterprise.mtls.googleapis.com/", "name": "recaptchaenterprise", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"assessments": {"methods": {"annotate": {"description": "Annotates a previously created Assessment to provide additional information on whether the event turned out to be authentic or fraudulent.", "flatPath": "v1/projects/{projectsId}/assessments/{assessmentsId}:annotate", "httpMethod": "POST", "id": "recaptchaenterprise.projects.assessments.annotate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The resource name of the Assessment, in the format `projects/{project}/assessments/{assessment}`.", "location": "path", "pattern": "^projects/[^/]+/assessments/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:annotate", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates an Assessment of the likelihood an event is legitimate.", "flatPath": "v1/projects/{projectsId}/assessments", "httpMethod": "POST", "id": "recaptchaenterprise.projects.assessments.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in which the assessment is created, in the format `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/assessments", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1Assessment"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1Assessment"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "firewallpolicies": {"methods": {"create": {"description": "Creates a new FirewallPolicy, specifying conditions at which reCAPTCHA Enterprise actions can be executed. A project may have a maximum of 1000 policies.", "flatPath": "v1/projects/{projectsId}/firewallpolicies", "httpMethod": "POST", "id": "recaptchaenterprise.projects.firewallpolicies.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project this policy applies to, in the format `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/firewallpolicies", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified firewall policy.", "flatPath": "v1/projects/{projectsId}/firewallpolicies/{firewallpoliciesId}", "httpMethod": "DELETE", "id": "recaptchaenterprise.projects.firewallpolicies.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the policy to be deleted, in the format `projects/{project}/firewallpolicies/{firewallpolicy}`.", "location": "path", "pattern": "^projects/[^/]+/firewallpolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the specified firewall policy.", "flatPath": "v1/projects/{projectsId}/firewallpolicies/{firewallpoliciesId}", "httpMethod": "GET", "id": "recaptchaenterprise.projects.firewallpolicies.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the requested policy, in the format `projects/{project}/firewallpolicies/{firewallpolicy}`.", "location": "path", "pattern": "^projects/[^/]+/firewallpolicies/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns the list of all firewall policies that belong to a project.", "flatPath": "v1/projects/{projectsId}/firewallpolicies", "httpMethod": "GET", "id": "recaptchaenterprise.projects.firewallpolicies.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of policies to return. Default is 10. Max limit is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous. ListFirewallPoliciesRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to list the policies for, in the format `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/firewallpolicies", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1ListFirewallPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the specified firewall policy.", "flatPath": "v1/projects/{projectsId}/firewallpolicies/{firewallpoliciesId}", "httpMethod": "PATCH", "id": "recaptchaenterprise.projects.firewallpolicies.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name for the FirewallPolicy in the format `projects/{project}/firewallpolicies/{firewallpolicy}`.", "location": "path", "pattern": "^projects/[^/]+/firewallpolicies/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The mask to control which fields of the policy get updated. If the mask is not present, all fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "reorder": {"description": "Reorders all firewall policies.", "flatPath": "v1/projects/{projectsId}/firewallpolicies:reorder", "httpMethod": "POST", "id": "recaptchaenterprise.projects.firewallpolicies.reorder", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project to list the policies for, in the format `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/firewallpolicies:reorder", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "keys": {"methods": {"addIpOverride": {"description": "Adds an IP override to a key. The following restrictions hold: * The maximum number of IP overrides per key is 100. * For any conflict (such as IP already exists or IP part of an existing IP range), an error is returned.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}:addIpOverride", "httpMethod": "POST", "id": "recaptchaenterprise.projects.keys.addIpOverride", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key to which the IP override is added, in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:addIpOverride", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1AddIpOverrideRequest"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1AddIpOverrideResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "create": {"description": "Creates a new reCAPTCHA Enterprise key.", "flatPath": "v1/projects/{projectsId}/keys", "httpMethod": "POST", "id": "recaptchaenterprise.projects.keys.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The name of the project in which the key is created, in the format `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/keys", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1Key"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1Key"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes the specified key.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}", "httpMethod": "DELETE", "id": "recaptchaenterprise.projects.keys.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key to be deleted, in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleProtobufEmpty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Returns the specified key.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}", "httpMethod": "GET", "id": "recaptchaenterprise.projects.keys.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the requested key, in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1Key"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "getMetrics": {"description": "Get some aggregated metrics for a Key. This data can be used to build dashboards.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}/metrics", "httpMethod": "GET", "id": "recaptchaenterprise.projects.keys.getMetrics", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the requested metrics, in the format `projects/{project}/keys/{key}/metrics`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+/metrics$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1Metrics"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Returns the list of all keys that belong to a project.", "flatPath": "v1/projects/{projectsId}/keys", "httpMethod": "GET", "id": "recaptchaenterprise.projects.keys.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of keys to return. Default is 10. Max limit is 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous. ListKeysRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project that contains the keys that is listed, in the format `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/keys", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1ListKeysResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "listIpOverrides": {"description": "Lists all IP overrides for a key.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}:listIpOverrides", "httpMethod": "GET", "id": "recaptchaenterprise.projects.keys.listIpOverrides", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of overrides to return. Default is 10. Max limit is 100. If the number of overrides is less than the page_size, all overrides are returned. If the page size is more than 100, it is coerced to 100.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. The next_page_token value returned from a previous ListIpOverridesRequest, if any.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent key for which the IP overrides are listed, in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}:listIpOverrides", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1ListIpOverridesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "migrate": {"description": "Migrates an existing key from reCAPTCHA to reCAPTCHA Enterprise. Once a key is migrated, it can be used from either product. SiteVerify requests are billed as CreateAssessment calls. You must be authenticated as one of the current owners of the reCAPTCHA Key, and your user must have the reCAPTCHA Enterprise Admin IAM role in the destination project.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}:migrate", "httpMethod": "POST", "id": "recaptchaenterprise.projects.keys.migrate", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key to be migrated, in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:migrate", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1MigrateKeyRequest"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1Key"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates the specified key.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}", "httpMethod": "PATCH", "id": "recaptchaenterprise.projects.keys.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "Identifier. The resource name for the Key in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "Optional. The mask to control which fields of the key get updated. If the mask is not present, all fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1/{+name}", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1Key"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1Key"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "removeIpOverride": {"description": "Removes an IP override from a key. The following restrictions hold: * If the IP isn't found in an existing IP override, a `NOT_FOUND` error is returned. * If the IP is found in an existing IP override, but the override type does not match, a `NOT_FOUND` error is returned.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}:removeIpOverride", "httpMethod": "POST", "id": "recaptchaenterprise.projects.keys.removeIpOverride", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the key from which the IP override is removed, in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:removeIpOverride", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "retrieveLegacySecretKey": {"description": "Returns the secret key related to the specified public key. You must use the legacy secret key only in a 3rd party integration with legacy reCAPTCHA.", "flatPath": "v1/projects/{projectsId}/keys/{keysId}:retrieveLegacySecretKey", "httpMethod": "GET", "id": "recaptchaenterprise.projects.keys.retrieveLegacySecretKey", "parameterOrder": ["key"], "parameters": {"key": {"description": "Required. The public key name linked to the requested secret key in the format `projects/{project}/keys/{key}`.", "location": "path", "pattern": "^projects/[^/]+/keys/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+key}:retrieveLegacySecretKey", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1RetrieveLegacySecretKeyResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "relatedaccountgroupmemberships": {"methods": {"search": {"description": "Search group memberships related to a given account.", "flatPath": "v1/projects/{projectsId}/relatedaccountgroupmemberships:search", "httpMethod": "POST", "id": "recaptchaenterprise.projects.relatedaccountgroupmemberships.search", "parameterOrder": ["project"], "parameters": {"project": {"description": "Required. The name of the project to search related account group memberships from. Specify the project name in the following format: `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+project}/relatedaccountgroupmemberships:search", "request": {"$ref": "GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsRequest"}, "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "relatedaccountgroups": {"methods": {"list": {"description": "List groups of related accounts.", "flatPath": "v1/projects/{projectsId}/relatedaccountgroups", "httpMethod": "GET", "id": "recaptchaenterprise.projects.relatedaccountgroups.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of groups to return. The service might return fewer than this value. If unspecified, at most 50 groups are returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListRelatedAccountGroups` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListRelatedAccountGroups` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The name of the project to list related account groups from, in the format `projects/{project}`.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/relatedaccountgroups", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"memberships": {"methods": {"list": {"description": "Get memberships in a group of related accounts.", "flatPath": "v1/projects/{projectsId}/relatedaccountgroups/{relatedaccountgroupsId}/memberships", "httpMethod": "GET", "id": "recaptchaenterprise.projects.relatedaccountgroups.memberships.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of accounts to return. The service might return fewer than this value. If unspecified, at most 50 accounts are returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListRelatedAccountGroupMemberships` call. When paginating, all other parameters provided to `ListRelatedAccountGroupMemberships` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The resource name for the related account group in the format `projects/{project}/relatedaccountgroups/{relatedaccountgroup}`.", "location": "path", "pattern": "^projects/[^/]+/relatedaccountgroups/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+parent}/memberships", "response": {"$ref": "GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupMembershipsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "********", "rootUrl": "https://recaptchaenterprise.googleapis.com/", "schemas": {"GoogleCloudRecaptchaenterpriseV1AccountDefenderAssessment": {"description": "Account defender risk assessment.", "id": "GoogleCloudRecaptchaenterpriseV1AccountDefenderAssessment", "properties": {"labels": {"description": "Output only. Labels for this request.", "items": {"enum": ["ACCOUNT_DEFENDER_LABEL_UNSPECIFIED", "PROFILE_MATCH", "SUSPICIOUS_LOGIN_ACTIVITY", "SUSPICIOUS_ACCOUNT_CREATION", "RELATED_ACCOUNTS_NUMBER_HIGH"], "enumDescriptions": ["Default unspecified type.", "The request matches a known good profile for the user.", "The request is potentially a suspicious login event and must be further verified either through multi-factor authentication or another system.", "The request matched a profile that previously had suspicious account creation behavior. This can mean that this is a fake account.", "The account in the request has a high number of related accounts. It does not necessarily imply that the account is bad but can require further investigation."], "type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AccountVerificationInfo": {"description": "Information about account verification, used for identity verification.", "id": "GoogleCloudRecaptchaenterpriseV1AccountVerificationInfo", "properties": {"endpoints": {"description": "Optional. Endpoints that can be used for identity verification.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1EndpointVerificationInfo"}, "type": "array"}, "languageCode": {"description": "Optional. Language code preference for the verification message, set as a IETF BCP 47 language code.", "type": "string"}, "latestVerificationResult": {"description": "Output only. Result of the latest account verification challenge.", "enum": ["RESULT_UNSPECIFIED", "SUCCESS_USER_VERIFIED", "ERROR_USER_NOT_VERIFIED", "ERROR_SITE_ONBOARDING_INCOMPLETE", "ERROR_RECIPIENT_NOT_ALLOWED", "ERROR_RECIPIENT_ABUSE_LIMIT_EXHAUSTED", "ERROR_CRITICAL_INTERNAL", "ERROR_CUSTOMER_QUOTA_EXHAUSTED", "ERROR_VERIFICATION_BYPASSED", "ERROR_VERDICT_MISMATCH"], "enumDescriptions": ["No information about the latest account verification.", "The user was successfully verified. This means the account verification challenge was successfully completed.", "The user failed the verification challenge.", "The site is not properly onboarded to use the account verification feature.", "The recipient is not allowed for account verification. This can occur during integration but should not occur in production.", "The recipient has already been sent too many verification codes in a short amount of time.", "The verification flow could not be completed due to a critical internal error.", "The client has exceeded their two factor request quota for this period of time.", "The request cannot be processed at the time because of an incident. This bypass can be restricted to a problematic destination email domain, a customer, or could affect the entire service.", "The request parameters do not match with the token provided and cannot be processed."], "readOnly": true, "type": "string"}, "username": {"deprecated": true, "description": "Username of the account that is being verified. Deprecated. Customers should now provide the `account_id` field in `event.user_info`.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AddIpOverrideRequest": {"description": "The AddIpOverride request message.", "id": "GoogleCloudRecaptchaenterpriseV1AddIpOverrideRequest", "properties": {"ipOverrideData": {"$ref": "GoogleCloudRecaptchaenterpriseV1IpOverrideData", "description": "Required. IP override added to the key."}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AddIpOverrideResponse": {"description": "Response for AddIpOverride.", "id": "GoogleCloudRecaptchaenterpriseV1AddIpOverrideResponse", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AndroidKeySettings": {"description": "Settings specific to keys that can be used by Android apps.", "id": "GoogleCloudRecaptchaenterpriseV1AndroidKeySettings", "properties": {"allowAllPackageNames": {"description": "Optional. If set to true, allowed_package_names are not enforced.", "type": "boolean"}, "allowedPackageNames": {"description": "Optional. Android package names of apps allowed to use the key. Example: 'com.companyname.appname'", "items": {"type": "string"}, "type": "array"}, "supportNonGoogleAppStoreDistribution": {"description": "Optional. Set to true for keys that are used in an Android application that is available for download in app stores in addition to the Google Play Store.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest": {"description": "The request message to annotate an Assessment.", "id": "GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentRequest", "properties": {"accountId": {"description": "Optional. A stable account identifier to apply to the assessment. This is an alternative to setting `account_id` in `CreateAssessment`, for example when a stable account identifier is not yet known in the initial request.", "type": "string"}, "annotation": {"description": "Optional. The annotation that is assigned to the Event. This field can be left empty to provide reasons that apply to an event without concluding whether the event is legitimate or fraudulent.", "enum": ["ANNOTATION_UNSPECIFIED", "LEGITIMATE", "FRAUDULENT", "PASSWORD_CORRECT", "PASSWORD_INCORRECT"], "enumDeprecated": [false, false, false, true, true], "enumDescriptions": ["Default unspecified type.", "Provides information that the event turned out to be legitimate.", "Provides information that the event turned out to be fraudulent.", "Provides information that the event was related to a login event in which the user typed the correct password. Deprecated, prefer indicating CORRECT_PASSWORD through the reasons field instead.", "Provides information that the event was related to a login event in which the user typed the incorrect password. Deprecated, prefer indicating INCORRECT_PASSWORD through the reasons field instead."], "type": "string"}, "hashedAccountId": {"description": "Optional. A stable hashed account identifier to apply to the assessment. This is an alternative to setting `hashed_account_id` in `CreateAssessment`, for example when a stable account identifier is not yet known in the initial request.", "format": "byte", "type": "string"}, "reasons": {"description": "Optional. Reasons for the annotation that are assigned to the event.", "items": {"enum": ["REASON_UNSPECIFIED", "CHARGEBACK", "CHARGEBACK_FRAUD", "CHARGEBACK_DISPUTE", "REFUND", "REFUND_FRAUD", "TRANSACTION_ACCEPTED", "TRANSACTION_DECLINED", "PAYMENT_HEURISTICS", "INITIATED_TWO_FACTOR", "PASSED_TWO_FACTOR", "FAILED_TWO_FACTOR", "CORRECT_PASSWORD", "INCORRECT_PASSWORD", "SOCIAL_SPAM"], "enumDescriptions": ["Unspecified reason. Do not use.", "Indicates that the transaction had a chargeback issued with no other details. When possible, specify the type by using CHARGEBACK_FRAUD or CHARGEBACK_DISPUTE instead.", "Indicates that the transaction had a chargeback issued related to an alleged unauthorized transaction from the cardholder's perspective (for example, the card number was stolen).", "Indicates that the transaction had a chargeback issued related to the cardholder having provided their card details but allegedly not being satisfied with the purchase (for example, misrepresentation, attempted cancellation).", "Indicates that the completed payment transaction was refunded by the seller.", "Indicates that the completed payment transaction was determined to be fraudulent by the seller, and was cancelled and refunded as a result.", "Indicates that the payment transaction was accepted, and the user was charged.", "Indicates that the payment transaction was declined, for example due to invalid card details.", "Indicates the transaction associated with the assessment is suspected of being fraudulent based on the payment method, billing details, shipping address or other transaction information.", "Indicates that the user was served a 2FA challenge. An old assessment with `ENUM_VALUES.INITIATED_TWO_FACTOR` reason that has not been overwritten with `PASSED_TWO_FACTOR` is treated as an abandoned 2FA flow. This is equivalent to `FAILED_TWO_FACTOR`.", "Indicates that the user passed a 2FA challenge.", "Indicates that the user failed a 2FA challenge.", "Indicates the user provided the correct password.", "Indicates the user provided an incorrect password.", "Indicates that the user sent unwanted and abusive messages to other users of the platform, such as spam, scams, phishing, or social engineering."], "type": "string"}, "type": "array"}, "transactionEvent": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionEvent", "description": "Optional. If the assessment is part of a payment transaction, provide details on payment lifecycle events that occur in the transaction."}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentResponse": {"description": "Empty response for AnnotateAssessment.", "id": "GoogleCloudRecaptchaenterpriseV1AnnotateAssessmentResponse", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AppleDeveloperId": {"description": "Contains fields that are required to perform Apple-specific integrity checks.", "id": "GoogleCloudRecaptchaenterpriseV1AppleDeveloperId", "properties": {"keyId": {"description": "Required. The Apple developer key ID (10-character string).", "type": "string"}, "privateKey": {"description": "Required. Input only. A private key (downloaded as a text file with a .p8 file extension) generated for your Apple Developer account. Ensure that Apple DeviceCheck is enabled for the private key.", "type": "string"}, "teamId": {"description": "Required. The Apple team ID (10-character string) owning the provisioning profile used to build your application.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1Assessment": {"description": "A reCAPTCHA Enterprise assessment resource.", "id": "GoogleCloudRecaptchaenterpriseV1Assessment", "properties": {"accountDefenderAssessment": {"$ref": "GoogleCloudRecaptchaenterpriseV1AccountDefenderAssessment", "description": "Output only. Assessment returned by account defender when an account identifier is provided.", "readOnly": true}, "accountVerification": {"$ref": "GoogleCloudRecaptchaenterpriseV1AccountVerificationInfo", "description": "Optional. Account verification information for identity verification. The assessment event must include a token and site key to use this feature."}, "assessmentEnvironment": {"$ref": "GoogleCloudRecaptchaenterpriseV1AssessmentEnvironment", "description": "Optional. The environment creating the assessment. This describes your environment (the system invoking CreateAssessment), NOT the environment of your user."}, "event": {"$ref": "GoogleCloudRecaptchaenterpriseV1Event", "description": "Optional. The event being assessed."}, "firewallPolicyAssessment": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicyAssessment", "description": "Output only. Assessment returned when firewall policies belonging to the project are evaluated using the field firewall_policy_evaluation.", "readOnly": true}, "fraudPreventionAssessment": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessment", "description": "Output only. Assessment returned by Fraud Prevention when TransactionData is provided.", "readOnly": true}, "fraudSignals": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudSignals", "description": "Output only. Fraud Signals specific to the users involved in a payment transaction.", "readOnly": true}, "name": {"description": "Output only. Identifier. The resource name for the Assessment in the format `projects/{project}/assessments/{assessment}`.", "readOnly": true, "type": "string"}, "phoneFraudAssessment": {"$ref": "GoogleCloudRecaptchaenterpriseV1PhoneFraudAssessment", "description": "Output only. Assessment returned when a site key, a token, and a phone number as `user_id` are provided. Account defender and SMS toll fraud protection need to be enabled.", "readOnly": true}, "privatePasswordLeakVerification": {"$ref": "GoogleCloudRecaptchaenterpriseV1PrivatePasswordLeakVerification", "description": "Optional. The private password leak verification field contains the parameters that are used to to check for leaks privately without sharing user credentials."}, "riskAnalysis": {"$ref": "GoogleCloudRecaptchaenterpriseV1RiskAnalysis", "description": "Output only. The risk analysis result for the event being assessed.", "readOnly": true}, "tokenProperties": {"$ref": "GoogleCloudRecaptchaenterpriseV1TokenProperties", "description": "Output only. Properties of the provided event token.", "readOnly": true}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1AssessmentEnvironment": {"description": "The environment creating the assessment. This describes your environment (the system invoking CreateAssessment), NOT the environment of your user.", "id": "GoogleCloudRecaptchaenterpriseV1AssessmentEnvironment", "properties": {"client": {"description": "Optional. Identifies the client module initiating the CreateAssessment request. This can be the link to the client module's project. Examples include: - \"github.com/GoogleCloudPlatform/recaptcha-enterprise-google-tag-manager\" - \"cloud.google.com/recaptcha/docs/implement-waf-akamai\" - \"cloud.google.com/recaptcha/docs/implement-waf-cloudflare\" - \"wordpress.org/plugins/recaptcha-something\"", "type": "string"}, "version": {"description": "Optional. The version of the client module. For example, \"1.0.0\".", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1Bot": {"description": "Bot information and metadata.", "id": "GoogleCloudRecaptchaenterpriseV1Bot", "properties": {"botType": {"description": "Optional. Enumerated field representing the type of bot.", "enum": ["BOT_TYPE_UNSPECIFIED", "AI_AGENT", "CONTENT_SCRAPER", "SEARCH_INDEXER"], "enumDescriptions": ["Default unspecified type.", "Software program that interacts with a site and performs tasks autonomously.", "Software that extracts specific data from sites for use.", "Software that crawls sites and stores content for the purpose of efficient retrieval, likely as part of a search engine."], "type": "string"}, "name": {"description": "Optional. Enumerated string value that indicates the identity of the bot, formatted in kebab-case.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ChallengeMetrics": {"description": "Metrics related to challenges.", "id": "GoogleCloudRecaptchaenterpriseV1ChallengeMetrics", "properties": {"failedCount": {"description": "Count of submitted challenge solutions that were incorrect or otherwise deemed suspicious such that a subsequent challenge was triggered.", "format": "int64", "type": "string"}, "nocaptchaCount": {"description": "Count of nocaptchas (successful verification without a challenge) issued.", "format": "int64", "type": "string"}, "pageloadCount": {"description": "Count of reCAPTCHA checkboxes or badges rendered. This is mostly equivalent to a count of pageloads for pages that include reCAPTCHA.", "format": "int64", "type": "string"}, "passedCount": {"description": "Count of nocaptchas (successful verification without a challenge) plus submitted challenge solutions that were correct and resulted in verification.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1EndpointVerificationInfo": {"description": "Information about a verification endpoint that can be used for 2FA.", "id": "GoogleCloudRecaptchaenterpriseV1EndpointVerificationInfo", "properties": {"emailAddress": {"description": "Email address for which to trigger a verification request.", "type": "string"}, "lastVerificationTime": {"description": "Output only. Timestamp of the last successful verification for the endpoint, if any.", "format": "google-datetime", "readOnly": true, "type": "string"}, "phoneNumber": {"description": "Phone number for which to trigger a verification request. Should be given in E.164 format.", "type": "string"}, "requestToken": {"description": "Output only. Token to provide to the client to trigger endpoint verification. It must be used within 15 minutes.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1Event": {"description": "The event being assessed.", "id": "GoogleCloudRecaptchaenterpriseV1Event", "properties": {"expectedAction": {"description": "Optional. The expected action for this type of event. This should be the same action provided at token generation time on client-side platforms already integrated with recaptcha enterprise.", "type": "string"}, "express": {"description": "Optional. Flag for a reCAPTCHA express request for an assessment without a token. If enabled, `site_key` must reference an Express site key.", "type": "boolean"}, "firewallPolicyEvaluation": {"description": "Optional. Flag for enabling firewall policy config assessment. If this flag is enabled, the firewall policy is evaluated and a suggested firewall action is returned in the response.", "type": "boolean"}, "fraudPrevention": {"description": "Optional. The Fraud Prevention setting for this assessment.", "enum": ["FRAUD_PREVENTION_UNSPECIFIED", "ENABLED", "DISABLED"], "enumDescriptions": ["Default, unspecified setting. `fraud_prevention_assessment` is returned if `transaction_data` is present in `Event` and Fraud Prevention is enabled in the Google Cloud console.", "Enable Fraud Prevention for this assessment, if Fraud Prevention is enabled in the Google Cloud console.", "Disable Fraud Prevention for this assessment, regardless of the Google Cloud console settings."], "type": "string"}, "hashedAccountId": {"deprecated": true, "description": "Optional. Deprecated: use `user_info.account_id` instead. Unique stable hashed user identifier for the request. The identifier must be hashed using hmac-sha256 with stable secret.", "format": "byte", "type": "string"}, "headers": {"description": "Optional. HTTP header information about the request.", "items": {"type": "string"}, "type": "array"}, "ja3": {"description": "Optional. JA3 fingerprint for SSL clients. To learn how to compute this fingerprint, please refer to https://github.com/salesforce/ja3.", "type": "string"}, "ja4": {"description": "Optional. JA4 fingerprint for SSL clients. To learn how to compute this fingerprint, please refer to https://github.com/FoxIO-LLC/ja4.", "type": "string"}, "requestedUri": {"description": "Optional. The URI resource the user requested that triggered an assessment.", "type": "string"}, "siteKey": {"description": "Optional. The site key that was used to invoke reCAPTCHA Enterprise on your site and generate the token.", "type": "string"}, "token": {"description": "Optional. The user response token provided by the reCAPTCHA Enterprise client-side integration on your site.", "type": "string"}, "transactionData": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionData", "description": "Optional. Data describing a payment transaction to be assessed. Sending this data enables reCAPTCHA Enterprise Fraud Prevention and the FraudPreventionAssessment component in the response."}, "userAgent": {"description": "Optional. The user agent present in the request from the user's device related to this event.", "type": "string"}, "userInfo": {"$ref": "GoogleCloudRecaptchaenterpriseV1UserInfo", "description": "Optional. Information about the user that generates this event, when they can be identified. They are often identified through the use of an account for logged-in requests or login/registration requests, or by providing user identifiers for guest actions like checkout."}, "userIpAddress": {"description": "Optional. The IP address in the request from the user's device related to this event.", "type": "string"}, "wafTokenAssessment": {"description": "Optional. Flag for running WAF token assessment. If enabled, the token must be specified, and have been created by a WAF-enabled key.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ExpressKeySettings": {"description": "Settings specific to keys that can be used for reCAPTCHA Express.", "id": "GoogleCloudRecaptchaenterpriseV1ExpressKeySettings", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallAction": {"description": "An individual action. Each action represents what to do if a policy matches.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallAction", "properties": {"allow": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallActionAllowAction", "description": "The user request did not match any policy and should be allowed access to the requested resource."}, "block": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallActionBlockAction", "description": "This action denies access to a given page. The user gets an HTTP error code."}, "includeRecaptchaScript": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallActionIncludeRecaptchaScriptAction", "description": "This action injects reCAPTCHA JavaScript code into the HTML page returned by the site backend."}, "redirect": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallActionRedirectAction", "description": "This action redirects the request to a reCAPTCHA interstitial to attach a token."}, "setHeader": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallActionSetHeaderAction", "description": "This action sets a custom header but allow the request to continue to the customer backend."}, "substitute": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallActionSubstituteAction", "description": "This action transparently serves a different page to an offending user."}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallActionAllowAction": {"description": "An allow action continues processing a request unimpeded.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallActionAllowAction", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallActionBlockAction": {"description": "A block action serves an HTTP error code a prevents the request from hitting the backend.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallActionBlockAction", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallActionIncludeRecaptchaScriptAction": {"description": "An include reCAPTCHA script action involves injecting reCAPTCHA JavaScript code into the HTML returned by the site backend. This reCAPTCHA script is tasked with collecting user signals on the requested web page, issuing tokens as a cookie within the site domain, and enabling their utilization in subsequent page requests.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallActionIncludeRecaptchaScriptAction", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallActionRedirectAction": {"description": "A redirect action returns a 307 (temporary redirect) response, pointing the user to a reCAPTCHA interstitial page to attach a token.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallActionRedirectAction", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallActionSetHeaderAction": {"description": "A set header action sets a header and forwards the request to the backend. This can be used to trigger custom protection implemented on the backend.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallActionSetHeaderAction", "properties": {"key": {"description": "Optional. The header key to set in the request to the backend server.", "type": "string"}, "value": {"description": "Optional. The header value to set in the request to the backend server.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallActionSubstituteAction": {"description": "A substitute action transparently serves a different page than the one requested.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallActionSubstituteAction", "properties": {"path": {"description": "Optional. The address to redirect to. The target is a relative path in the current host. Example: \"/blog/404.html\".", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallPolicy": {"description": "A FirewallPolicy represents a single matching pattern and resulting actions to take.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy", "properties": {"actions": {"description": "Optional. The actions that the caller should take regarding user access. There should be at most one terminal action. A terminal action is any action that forces a response, such as `AllowAction`, `BlockAction` or `SubstituteAction`. Zero or more non-terminal actions such as `SetHeader` might be specified. A single policy can contain up to 16 actions.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallAction"}, "type": "array"}, "condition": {"description": "Optional. A CEL (Common Expression Language) conditional expression that specifies if this policy applies to an incoming user request. If this condition evaluates to true and the requested path matched the path pattern, the associated actions should be executed by the caller. The condition string is checked for CEL syntax correctness on creation. For more information, see the [CEL spec](https://github.com/google/cel-spec) and its [language definition](https://github.com/google/cel-spec/blob/master/doc/langdef.md). A condition has a max length of 500 characters.", "type": "string"}, "description": {"description": "Optional. A description of what this policy aims to achieve, for convenience purposes. The description can at most include 256 UTF-8 characters.", "type": "string"}, "name": {"description": "Identifier. The resource name for the FirewallPolicy in the format `projects/{project}/firewallpolicies/{firewallpolicy}`.", "type": "string"}, "path": {"description": "Optional. The path for which this policy applies, specified as a glob pattern. For more information on glob, see the [manual page](https://man7.org/linux/man-pages/man7/glob.7.html). A path has a max length of 200 characters.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FirewallPolicyAssessment": {"description": "Policy config assessment.", "id": "GoogleCloudRecaptchaenterpriseV1FirewallPolicyAssessment", "properties": {"error": {"$ref": "GoogleRpcStatus", "description": "Output only. If the processing of a policy config fails, an error is populated and the firewall_policy is left empty.", "readOnly": true}, "firewallPolicy": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy", "description": "Output only. The policy that matched the request. If more than one policy may match, this is the first match. If no policy matches the incoming request, the policy field is left empty.", "readOnly": true}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessment": {"description": "Assessment for Fraud Prevention.", "id": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessment", "properties": {"behavioralTrustVerdict": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentBehavioralTrustVerdict", "description": "Output only. Assessment of this transaction for behavioral trust.", "readOnly": true}, "cardTestingVerdict": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentCardTestingVerdict", "description": "Output only. Assessment of this transaction for risk of being part of a card testing attack.", "readOnly": true}, "riskReasons": {"description": "Output only. Reasons why the transaction is probably fraudulent and received a high transaction risk score.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentRiskReason"}, "readOnly": true, "type": "array"}, "stolenInstrumentVerdict": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentStolenInstrumentVerdict", "description": "Output only. Assessment of this transaction for risk of a stolen instrument.", "readOnly": true}, "transactionRisk": {"description": "Output only. Probability of this transaction being fraudulent. Summarizes the combined risk of attack vectors below. Values are from 0.0 (lowest) to 1.0 (highest).", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentBehavioralTrustVerdict": {"description": "Information about behavioral trust of the transaction.", "id": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentBehavioralTrustVerdict", "properties": {"trust": {"description": "Output only. Probability of this transaction attempt being executed in a behaviorally trustworthy way. Values are from 0.0 (lowest) to 1.0 (highest).", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentCardTestingVerdict": {"description": "Information about card testing fraud, where an adversary is testing fraudulently obtained cards or brute forcing their details.", "id": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentCardTestingVerdict", "properties": {"risk": {"description": "Output only. Probability of this transaction attempt being part of a card testing attack. Values are from 0.0 (lowest) to 1.0 (highest).", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentRiskReason": {"description": "Risk reasons applicable to the Fraud Prevention assessment.", "id": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentRiskReason", "properties": {"reason": {"description": "Output only. Risk reasons applicable to the Fraud Prevention assessment.", "enum": ["REASON_UNSPECIFIED", "HIGH_TRANSACTION_VELOCITY", "EXCESSIVE_ENUMERATION_PATTERN", "SHORT_IDENTITY_HISTORY", "GEOLOCATION_DISCREPANCY", "ASSOCIATED_WITH_FRAUD_CLUSTER"], "enumDescriptions": ["Default unspecified type.", "A suspiciously high number of recent transactions have used identifiers present in this transaction.", "User is cycling through a suspiciously large number of identifiers, suggesting enumeration or validation attacks within a potential fraud network.", "User has a short history or no history in the reCAPTCHA network, suggesting the possibility of synthetic identity generation.", "Identifiers used in this transaction originate from an unusual or conflicting set of geolocations.", "This transaction is linked to a cluster of known fraudulent activity."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentStolenInstrumentVerdict": {"description": "Information about stolen instrument fraud, where the user is not the legitimate owner of the instrument being used for the purchase.", "id": "GoogleCloudRecaptchaenterpriseV1FraudPreventionAssessmentStolenInstrumentVerdict", "properties": {"risk": {"description": "Output only. Probability of this transaction being executed with a stolen instrument. Values are from 0.0 (lowest) to 1.0 (highest).", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudSignals": {"description": "Fraud signals describing users and cards involved in the transaction.", "id": "GoogleCloudRecaptchaenterpriseV1FraudSignals", "properties": {"cardSignals": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudSignalsCardSignals", "description": "Output only. Signals describing the payment card or cards used in this transaction.", "readOnly": true}, "userSignals": {"$ref": "GoogleCloudRecaptchaenterpriseV1FraudSignalsUserSignals", "description": "Output only. Signals describing the end user in this transaction.", "readOnly": true}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudSignalsCardSignals": {"description": "Signals describing the payment card used in this transaction.", "id": "GoogleCloudRecaptchaenterpriseV1FraudSignalsCardSignals", "properties": {"cardLabels": {"description": "Output only. The labels for the payment card in this transaction.", "items": {"enum": ["CARD_LABEL_UNSPECIFIED", "PREPAID", "VIRTUAL", "UNEXPECTED_LOCATION"], "enumDescriptions": ["No label specified.", "This card has been detected as prepaid.", "This card has been detected as virtual, such as a card number generated for a single transaction or merchant.", "This card has been detected as being used in an unexpected geographic location."], "type": "string"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1FraudSignalsUserSignals": {"description": "Signals describing the user involved in this transaction.", "id": "GoogleCloudRecaptchaenterpriseV1FraudSignalsUserSignals", "properties": {"activeDaysLowerBound": {"description": "Output only. This user (based on email, phone, and other identifiers) has been seen on the internet for at least this number of days.", "format": "int32", "readOnly": true, "type": "integer"}, "syntheticRisk": {"description": "Output only. Likelihood (from 0.0 to 1.0) this user includes synthetic components in their identity, such as a randomly generated email address, temporary phone number, or fake shipping address.", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1IOSKeySettings": {"description": "Settings specific to keys that can be used by iOS apps.", "id": "GoogleCloudRecaptchaenterpriseV1IOSKeySettings", "properties": {"allowAllBundleIds": {"description": "Optional. If set to true, allowed_bundle_ids are not enforced.", "type": "boolean"}, "allowedBundleIds": {"description": "Optional. iOS bundle ids of apps allowed to use the key. Example: 'com.companyname.productname.appname'", "items": {"type": "string"}, "type": "array"}, "appleDeveloperId": {"$ref": "GoogleCloudRecaptchaenterpriseV1AppleDeveloperId", "description": "Optional. Apple Developer account details for the app that is protected by the reCAPTCHA Key. reCAPTCHA leverages platform-specific checks like Apple App Attest and Apple DeviceCheck to protect your app from abuse. Providing these fields allows reCAPTCHA to get a better assessment of the integrity of your app."}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1IpOverrideData": {"description": "Information about the IP or IP range override.", "id": "GoogleCloudRecaptchaenterpriseV1IpOverrideData", "properties": {"ip": {"description": "Required. The IP address to override (can be IPv4, IPv6 or CIDR). The IP override must be a valid IPv4 or IPv6 address, or a CIDR range. The IP override must be a public IP address. Example of IPv4: *********** Example of IPv6: 2001:0000:130F:0000:0000:09C0:876A:130B Example of IPv4 with CIDR: ***********/24 Example of IPv6 with CIDR: 2001:0DB8:1234::/48", "type": "string"}, "overrideType": {"description": "Required. Describes the type of IP override.", "enum": ["OVERRIDE_TYPE_UNSPECIFIED", "ALLOW"], "enumDescriptions": ["Default override type that indicates this enum hasn't been specified.", "Allowlist the IP address; i.e. give a `risk_analysis.score` of 0.9 for all valid assessments."], "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1Key": {"description": "A key used to identify and configure applications (web and/or mobile) that use reCAPTCHA Enterprise.", "id": "GoogleCloudRecaptchaenterpriseV1Key", "properties": {"androidSettings": {"$ref": "GoogleCloudRecaptchaenterpriseV1AndroidKeySettings", "description": "Settings for keys that can be used by Android apps."}, "createTime": {"description": "Output only. The timestamp corresponding to the creation of this key.", "format": "google-datetime", "readOnly": true, "type": "string"}, "displayName": {"description": "Required. Human-readable display name of this key. Modifiable by user.", "type": "string"}, "expressSettings": {"$ref": "GoogleCloudRecaptchaenterpriseV1ExpressKeySettings", "description": "Settings for keys that can be used by reCAPTCHA Express."}, "iosSettings": {"$ref": "GoogleCloudRecaptchaenterpriseV1IOSKeySettings", "description": "Settings for keys that can be used by iOS apps."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Optional. See [Creating and managing labels] (https://cloud.google.com/recaptcha/docs/labels).", "type": "object"}, "name": {"description": "Identifier. The resource name for the Key in the format `projects/{project}/keys/{key}`.", "type": "string"}, "testingOptions": {"$ref": "GoogleCloudRecaptchaenterpriseV1TestingOptions", "description": "Optional. Options for user acceptance testing."}, "wafSettings": {"$ref": "GoogleCloudRecaptchaenterpriseV1WafSettings", "description": "Optional. Settings for WAF"}, "webSettings": {"$ref": "GoogleCloudRecaptchaenterpriseV1WebKeySettings", "description": "Settings for keys that can be used by websites."}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ListFirewallPoliciesResponse": {"description": "Response to request to list firewall policies belonging to a project.", "id": "GoogleCloudRecaptchaenterpriseV1ListFirewallPoliciesResponse", "properties": {"firewallPolicies": {"description": "Policy details.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1FirewallPolicy"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. It is set to empty if no policies remain in results.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ListIpOverridesResponse": {"description": "Response for ListIpOverrides.", "id": "GoogleCloudRecaptchaenterpriseV1ListIpOverridesResponse", "properties": {"ipOverrides": {"description": "IP Overrides details.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1IpOverrideData"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. If this field is empty, no keys remain in the results.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ListKeysResponse": {"description": "Response to request to list keys in a project.", "id": "GoogleCloudRecaptchaenterpriseV1ListKeysResponse", "properties": {"keys": {"description": "Key details.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1Key"}, "type": "array"}, "nextPageToken": {"description": "Token to retrieve the next page of results. It is set to empty if no keys remain in results.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupMembershipsResponse": {"description": "The response to a `ListRelatedAccountGroupMemberships` call.", "id": "GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupMembershipsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "relatedAccountGroupMemberships": {"description": "The memberships listed by the query.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1RelatedAccountGroupMembership"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupsResponse": {"description": "The response to a `ListRelatedAccountGroups` call.", "id": "GoogleCloudRecaptchaenterpriseV1ListRelatedAccountGroupsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "relatedAccountGroups": {"description": "The groups of related accounts listed by the query.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1RelatedAccountGroup"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1Metrics": {"description": "Metrics for a single Key.", "id": "GoogleCloudRecaptchaenterpriseV1Metrics", "properties": {"challengeMetrics": {"description": "Metrics are continuous and in order by dates, and in the granularity of day. Only challenge-based keys (CHECKBOX, INVISIBLE) have challenge-based data.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1ChallengeMetrics"}, "type": "array"}, "name": {"description": "Output only. Identifier. The name of the metrics, in the format `projects/{project}/keys/{key}/metrics`.", "readOnly": true, "type": "string"}, "scoreMetrics": {"description": "Metrics are continuous and in order by dates, and in the granularity of day. All Key types should have score-based data.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1ScoreMetrics"}, "type": "array"}, "startTime": {"description": "Inclusive start time aligned to a day in the America/Los_Angeles (Pacific) timezone.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1MigrateKeyRequest": {"description": "The migrate key request message.", "id": "GoogleCloudRecaptchaenterpriseV1MigrateKeyRequest", "properties": {"skipBillingCheck": {"description": "Optional. If true, skips the billing check. A reCAPTCHA Enterprise key or migrated key behaves differently than a reCAPTCHA (non-Enterprise version) key when you reach a quota limit (see https://cloud.google.com/recaptcha/quotas#quota_limit). To avoid any disruption of your usage, we check that a billing account is present. If your usage of reCAPTCHA is under the free quota, you can safely skip the billing check and proceed with the migration. See https://cloud.google.com/recaptcha/docs/billing-information.", "type": "boolean"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1PhoneFraudAssessment": {"description": "Assessment for Phone Fraud", "id": "GoogleCloudRecaptchaenterpriseV1PhoneFraudAssessment", "properties": {"smsTollFraudVerdict": {"$ref": "GoogleCloudRecaptchaenterpriseV1SmsTollFraudVerdict", "description": "Output only. Assessment of this phone event for risk of SMS toll fraud.", "readOnly": true}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1PrivatePasswordLeakVerification": {"description": "Private password leak verification info.", "id": "GoogleCloudRecaptchaenterpriseV1PrivatePasswordLeakVerification", "properties": {"encryptedLeakMatchPrefixes": {"description": "Output only. List of prefixes of the encrypted potential password leaks that matched the given parameters. They must be compared with the client-side decryption prefix of `reencrypted_user_credentials_hash`", "items": {"format": "byte", "type": "string"}, "readOnly": true, "type": "array"}, "encryptedUserCredentialsHash": {"description": "Optional. Encrypted Scrypt hash of the canonicalized username+password. It is re-encrypted by the server and returned through `reencrypted_user_credentials_hash`.", "format": "byte", "type": "string"}, "lookupHashPrefix": {"description": "Required. Exactly 26-bit prefix of the SHA-256 hash of the canonicalized username. It is used to look up password leaks associated with that hash prefix.", "format": "byte", "type": "string"}, "reencryptedUserCredentialsHash": {"description": "Output only. Corresponds to the re-encryption of the `encrypted_user_credentials_hash` field. It is used to match potential password leaks within `encrypted_leak_match_prefixes`.", "format": "byte", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1RelatedAccountGroup": {"description": "A group of related accounts.", "id": "GoogleCloudRecaptchaenterpriseV1RelatedAccountGroup", "properties": {"name": {"description": "Required. Identifier. The resource name for the related account group in the format `projects/{project}/relatedaccountgroups/{related_account_group}`.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1RelatedAccountGroupMembership": {"description": "A membership in a group of related accounts.", "id": "GoogleCloudRecaptchaenterpriseV1RelatedAccountGroupMembership", "properties": {"accountId": {"description": "The unique stable account identifier of the member. The identifier corresponds to an `account_id` provided in a previous `CreateAssessment` or `AnnotateAssessment` call.", "type": "string"}, "hashedAccountId": {"deprecated": true, "description": "Deprecated: use `account_id` instead. The unique stable hashed account identifier of the member. The identifier corresponds to a `hashed_account_id` provided in a previous `CreateAssessment` or `AnnotateAssessment` call.", "format": "byte", "type": "string"}, "name": {"description": "Required. Identifier. The resource name for this membership in the format `projects/{project}/relatedaccountgroups/{relatedaccountgroup}/memberships/{membership}`.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest": {"description": "The RemoveIpOverride request message.", "id": "GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideRequest", "properties": {"ipOverrideData": {"$ref": "GoogleCloudRecaptchaenterpriseV1IpOverrideData", "description": "Required. IP override to be removed from the key."}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideResponse": {"description": "Response for RemoveIpOverride.", "id": "GoogleCloudRecaptchaenterpriseV1RemoveIpOverrideResponse", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest": {"description": "The reorder firewall policies request message.", "id": "GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesRequest", "properties": {"names": {"description": "Required. A list containing all policy names, in the new order. Each name is in the format `projects/{project}/firewallpolicies/{firewallpolicy}`.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesResponse": {"description": "The reorder firewall policies response message.", "id": "GoogleCloudRecaptchaenterpriseV1ReorderFirewallPoliciesResponse", "properties": {}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1RetrieveLegacySecretKeyResponse": {"description": "Secret key is used only in legacy reCAPTCHA. It must be used in a 3rd party integration with legacy reCAPTCHA.", "id": "GoogleCloudRecaptchaenterpriseV1RetrieveLegacySecretKeyResponse", "properties": {"legacySecretKey": {"description": "The secret key (also known as shared secret) authorizes communication between your application backend and the reCAPTCHA Enterprise server to create an assessment. The secret key needs to be kept safe for security purposes.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1RiskAnalysis": {"description": "Risk analysis result for an event.", "id": "GoogleCloudRecaptchaenterpriseV1RiskAnalysis", "properties": {"challenge": {"description": "Output only. Challenge information for SCORE_AND_CHALLENGE and INVISIBLE keys", "enum": ["CHALLENGE_UNSPECIFIED", "NOCAPTCHA", "PASSED", "FAILED"], "enumDescriptions": ["Default unspecified type.", "No challenge was presented for solving.", "A solution was submitted that was correct.", "A solution was submitted that was incorrect or otherwise deemed suspicious."], "readOnly": true, "type": "string"}, "extendedVerdictReasons": {"description": "Output only. Extended verdict reasons to be used for experimentation only. The set of possible reasons is subject to change.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "reasons": {"description": "Output only. Reasons contributing to the risk analysis verdict.", "items": {"enum": ["CLASSIFICATION_REASON_UNSPECIFIED", "AUTOMATION", "UNEXPECTED_ENVIRONMENT", "TOO_MUCH_TRAFFIC", "UNEXPECTED_USAGE_PATTERNS", "LOW_CONFIDENCE_SCORE", "SUSPECTED_CARDING", "SUSPECTED_CHARGEBACK"], "enumDescriptions": ["Default unspecified type.", "Interactions matched the behavior of an automated agent.", "The event originated from an illegitimate environment.", "Traffic volume from the event source is higher than normal.", "Interactions with the site were significantly different than expected patterns.", "Too little traffic has been received from this site thus far to generate quality risk analysis.", "The request matches behavioral characteristics of a carding attack.", "The request matches behavioral characteristics of chargebacks for fraud."], "type": "string"}, "readOnly": true, "type": "array"}, "score": {"description": "Output only. Legitimate event score from 0.0 to 1.0. (1.0 means very likely legitimate traffic while 0.0 means very likely non-legitimate traffic).", "format": "float", "readOnly": true, "type": "number"}, "verifiedBots": {"description": "Output only. Bots with identities that have been verified by reCAPTCHA and detected in the event.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1Bot"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ScoreDistribution": {"description": "Score distribution.", "id": "GoogleCloudRecaptchaenterpriseV1ScoreDistribution", "properties": {"scoreBuckets": {"additionalProperties": {"format": "int64", "type": "string"}, "description": "Map key is score value multiplied by 100. The scores are discrete values between [0, 1]. The maximum number of buckets is on order of a few dozen, but typically much lower (ie. 10).", "type": "object"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1ScoreMetrics": {"description": "Metrics related to scoring.", "id": "GoogleCloudRecaptchaenterpriseV1ScoreMetrics", "properties": {"actionMetrics": {"additionalProperties": {"$ref": "GoogleCloudRecaptchaenterpriseV1ScoreDistribution"}, "description": "Action-based metrics. The map key is the action name which specified by the site owners at time of the \"execute\" client-side call.", "type": "object"}, "overallMetrics": {"$ref": "GoogleCloudRecaptchaenterpriseV1ScoreDistribution", "description": "Aggregated score metrics for all traffic."}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsRequest": {"description": "The request message to search related account group memberships.", "id": "GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsRequest", "properties": {"accountId": {"description": "Optional. The unique stable account identifier used to search connections. The identifier should correspond to an `account_id` provided in a previous `CreateAssessment` or `AnnotateAssessment` call. Either hashed_account_id or account_id must be set, but not both.", "type": "string"}, "hashedAccountId": {"deprecated": true, "description": "Optional. Deprecated: use `account_id` instead. The unique stable hashed account identifier used to search connections. The identifier should correspond to a `hashed_account_id` provided in a previous `CreateAssessment` or `AnnotateAssessment` call. Either hashed_account_id or account_id must be set, but not both.", "format": "byte", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of groups to return. The service might return fewer than this value. If unspecified, at most 50 groups are returned. The maximum value is 1000; values above 1000 are coerced to 1000.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `SearchRelatedAccountGroupMemberships` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `SearchRelatedAccountGroupMemberships` must match the call that provided the page token.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsResponse": {"description": "The response to a `SearchRelatedAccountGroupMemberships` call.", "id": "GoogleCloudRecaptchaenterpriseV1SearchRelatedAccountGroupMembershipsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "relatedAccountGroupMemberships": {"description": "The queried memberships.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1RelatedAccountGroupMembership"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1SmsTollFraudVerdict": {"description": "Information about SMS toll fraud.", "id": "GoogleCloudRecaptchaenterpriseV1SmsTollFraudVerdict", "properties": {"reasons": {"description": "Output only. Reasons contributing to the SMS toll fraud verdict.", "items": {"enum": ["SMS_TOLL_FRAUD_REASON_UNSPECIFIED", "INVALID_PHONE_NUMBER"], "enumDescriptions": ["Default unspecified reason", "The provided phone number was invalid"], "type": "string"}, "readOnly": true, "type": "array"}, "risk": {"description": "Output only. Probability of an SMS event being fraudulent. Values are from 0.0 (lowest) to 1.0 (highest).", "format": "float", "readOnly": true, "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TestingOptions": {"description": "Options for user acceptance testing.", "id": "GoogleCloudRecaptchaenterpriseV1TestingOptions", "properties": {"testingChallenge": {"description": "Optional. For challenge-based keys only (CHECKBOX, INVISIBLE), all challenge requests for this site return nocaptcha if NOCAPTCHA, or an unsolvable challenge if CHALLENGE.", "enum": ["TESTING_CHALLENGE_UNSPECIFIED", "NOCAPTCHA", "UNSOLVABLE_CHALLENGE"], "enumDescriptions": ["Perform the normal risk analysis and return either nocaptcha or a challenge depending on risk and trust factors.", "Challenge requests for this key always return a nocaptcha, which does not require a solution.", "Challenge requests for this key always return an unsolvable challenge."], "type": "string"}, "testingScore": {"description": "Optional. All assessments for this Key return this score. Must be between 0 (likely not legitimate) and 1 (likely legitimate) inclusive.", "format": "float", "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TokenProperties": {"description": "Properties of the provided event token.", "id": "GoogleCloudRecaptchaenterpriseV1TokenProperties", "properties": {"action": {"description": "Output only. Action name provided at token generation.", "readOnly": true, "type": "string"}, "androidPackageName": {"description": "Output only. The name of the Android package with which the token was generated (Android keys only).", "readOnly": true, "type": "string"}, "createTime": {"description": "Output only. The timestamp corresponding to the generation of the token.", "format": "google-datetime", "readOnly": true, "type": "string"}, "hostname": {"description": "Output only. The hostname of the page on which the token was generated (Web keys only).", "readOnly": true, "type": "string"}, "invalidReason": {"description": "Output only. Reason associated with the response when valid = false.", "enum": ["INVALID_REASON_UNSPECIFIED", "UNKNOWN_INVALID_REASON", "MALFORMED", "EXPIRED", "DUPE", "MISSING", "BROWSER_ERROR"], "enumDescriptions": ["Default unspecified type.", "If the failure reason was not accounted for.", "The provided user verification token was malformed.", "The user verification token had expired.", "The user verification had already been seen.", "The user verification token was not present.", "A retriable error (such as network failure) occurred on the browser. Could easily be simulated by an attacker."], "readOnly": true, "type": "string"}, "iosBundleId": {"description": "Output only. The ID of the iOS bundle with which the token was generated (iOS keys only).", "readOnly": true, "type": "string"}, "valid": {"description": "Output only. Whether the provided user response token is valid. When valid = false, the reason could be specified in invalid_reason or it could also be due to a user failing to solve a challenge or a sitekey mismatch (i.e the sitekey used to generate the token was different than the one specified in the assessment).", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TransactionData": {"description": "Transaction data associated with a payment protected by reCAPTCHA Enterprise.", "id": "GoogleCloudRecaptchaenterpriseV1TransactionData", "properties": {"billingAddress": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionDataAddress", "description": "Optional. Address associated with the payment method when applicable."}, "cardBin": {"description": "Optional. The Bank Identification Number - generally the first 6 or 8 digits of the card.", "type": "string"}, "cardLastFour": {"description": "Optional. The last four digits of the card.", "type": "string"}, "currencyCode": {"description": "Optional. The currency code in ISO-4217 format.", "type": "string"}, "gatewayInfo": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionDataGatewayInfo", "description": "Optional. Information about the payment gateway's response to the transaction."}, "items": {"description": "Optional. Items purchased in this transaction.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionDataItem"}, "type": "array"}, "merchants": {"description": "Optional. Information about the user or users fulfilling the transaction.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionDataUser"}, "type": "array"}, "paymentMethod": {"description": "Optional. The payment method for the transaction. The allowed values are: * credit-card * debit-card * gift-card * processor-{name} (If a third-party is used, for example, processor-paypal) * custom-{name} (If an alternative method is used, for example, custom-crypto)", "type": "string"}, "shippingAddress": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionDataAddress", "description": "Optional. Destination address if this transaction involves shipping a physical item."}, "shippingValue": {"description": "Optional. The value of shipping in the specified currency. 0 for free or no shipping.", "format": "double", "type": "number"}, "transactionId": {"description": "Unique identifier for the transaction. This custom identifier can be used to reference this transaction in the future, for example, labeling a refund or chargeback event. Two attempts at the same transaction should use the same transaction id.", "type": "string"}, "user": {"$ref": "GoogleCloudRecaptchaenterpriseV1TransactionDataUser", "description": "Optional. Information about the user paying/initiating the transaction."}, "value": {"description": "Optional. The decimal value of the transaction in the specified currency.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TransactionDataAddress": {"description": "Structured address format for billing and shipping addresses.", "id": "GoogleCloudRecaptchaenterpriseV1TransactionDataAddress", "properties": {"address": {"description": "Optional. The first lines of the address. The first line generally contains the street name and number, and further lines may include information such as an apartment number.", "items": {"type": "string"}, "type": "array"}, "administrativeArea": {"description": "Optional. The state, province, or otherwise administrative area of the address.", "type": "string"}, "locality": {"description": "Optional. The town/city of the address.", "type": "string"}, "postalCode": {"description": "Optional. The postal or ZIP code of the address.", "type": "string"}, "recipient": {"description": "Optional. The recipient name, potentially including information such as \"care of\".", "type": "string"}, "regionCode": {"description": "Optional. The CLDR country/region of the address.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TransactionDataGatewayInfo": {"description": "Details about the transaction from the gateway.", "id": "GoogleCloudRecaptchaenterpriseV1TransactionDataGatewayInfo", "properties": {"avsResponseCode": {"description": "Optional. AVS response code from the gateway (available only when reCAPTCHA Enterprise is called after authorization).", "type": "string"}, "cvvResponseCode": {"description": "Optional. CVV response code from the gateway (available only when reCAPTCHA Enterprise is called after authorization).", "type": "string"}, "gatewayResponseCode": {"description": "Optional. Gateway response code describing the state of the transaction.", "type": "string"}, "name": {"description": "Optional. Name of the gateway service (for example, stripe, square, paypal).", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TransactionDataItem": {"description": "Line items being purchased in this transaction.", "id": "GoogleCloudRecaptchaenterpriseV1TransactionDataItem", "properties": {"merchantAccountId": {"description": "Optional. When a merchant is specified, its corresponding account_id. Necessary to populate marketplace-style transactions.", "type": "string"}, "name": {"description": "Optional. The full name of the item.", "type": "string"}, "quantity": {"description": "Optional. The quantity of this item that is being purchased.", "format": "int64", "type": "string"}, "value": {"description": "Optional. The value per item that the user is paying, in the transaction currency, after discounts.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TransactionDataUser": {"description": "Details about a user's account involved in the transaction.", "id": "GoogleCloudRecaptchaenterpriseV1TransactionDataUser", "properties": {"accountId": {"description": "Optional. Unique account identifier for this user. If using account defender, this should match the hashed_account_id field. Otherwise, a unique and persistent identifier for this account.", "type": "string"}, "creationMs": {"description": "Optional. The epoch milliseconds of the user's account creation.", "format": "int64", "type": "string"}, "email": {"description": "Optional. The email address of the user.", "type": "string"}, "emailVerified": {"description": "Optional. Whether the email has been verified to be accessible by the user (OTP or similar).", "type": "boolean"}, "phoneNumber": {"description": "Optional. The phone number of the user, with country code.", "type": "string"}, "phoneVerified": {"description": "Optional. Whether the phone number has been verified to be accessible by the user (OTP or similar).", "type": "boolean"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1TransactionEvent": {"description": "Describes an event in the lifecycle of a payment transaction.", "id": "GoogleCloudRecaptchaenterpriseV1TransactionEvent", "properties": {"eventTime": {"description": "Optional. Timestamp when this transaction event occurred; otherwise assumed to be the time of the API call.", "format": "google-datetime", "type": "string"}, "eventType": {"description": "Optional. The type of this transaction event.", "enum": ["TRANSACTION_EVENT_TYPE_UNSPECIFIED", "MERCHANT_APPROVE", "MERCHANT_DENY", "MANUAL_REVIEW", "AUTHORIZATION", "AUTHORIZATION_DECLINE", "PAYMENT_CAPTURE", "PAYMENT_CAPTURE_DECLINE", "CANCEL", "CHARGEBACK_INQUIRY", "CHARGEBACK_ALERT", "FRAUD_NOTIFICATION", "CHARGEBACK", "CHARGEBACK_REPRESENTMENT", "CHARGEBACK_REVERSE", "REFUND_REQUEST", "REFUND_DECLINE", "REFUND", "REFUND_REVERSE"], "enumDescriptions": ["Default, unspecified event type.", "Indicates that the transaction is approved by the merchant. The accompanying reasons can include terms such as 'INHOUSE', 'ACCERTIFY', 'CYBERSOURCE', or 'MANUAL_REVIEW'.", "Indicates that the transaction is denied and concluded due to risks detected by the merchant. The accompanying reasons can include terms such as 'INHOUSE', 'ACCERTIFY', 'CYBERSOURCE', or 'MANUAL_REVIEW'.", "Indicates that the transaction is being evaluated by a human, due to suspicion or risk.", "Indicates that the authorization attempt with the card issuer succeeded.", "Indicates that the authorization attempt with the card issuer failed. The accompanying reasons can include Visa's '54' indicating that the card is expired, or '82' indicating that the CVV is incorrect.", "Indicates that the transaction is completed because the funds were settled.", "Indicates that the transaction could not be completed because the funds were not settled.", "Indicates that the transaction has been canceled. Specify the reason for the cancellation. For example, 'INSUFFICIENT_INVENTORY'.", "Indicates that the merchant has received a chargeback inquiry due to fraud for the transaction, requesting additional information before a fraud chargeback is officially issued and a formal chargeback notification is sent.", "Indicates that the merchant has received a chargeback alert due to fraud for the transaction. The process of resolving the dispute without involving the payment network is started.", "Indicates that a fraud notification is issued for the transaction, sent by the payment instrument's issuing bank because the transaction appears to be fraudulent. We recommend including TC40 or SAFE data in the `reason` field for this event type. For partial chargebacks, we recommend that you include an amount in the `value` field.", "Indicates that the merchant is informed by the payment network that the transaction has entered the chargeback process due to fraud. Reason code examples include Discover's '6005' and '6041'. For partial chargebacks, we recommend that you include an amount in the `value` field.", "Indicates that the transaction has entered the chargeback process due to fraud, and that the merchant has chosen to enter representment. Reason examples include Discover's '6005' and '6041'. For partial chargebacks, we recommend that you include an amount in the `value` field.", "Indicates that the transaction has had a fraud chargeback which was illegitimate and was reversed as a result. For partial chargebacks, we recommend that you include an amount in the `value` field.", "Indicates that the merchant has received a refund for a completed transaction. For partial refunds, we recommend that you include an amount in the `value` field. Reason example: 'TAX_EXEMPT' (partial refund of exempt tax)", "Indicates that the merchant has received a refund request for this transaction, but that they have declined it. For partial refunds, we recommend that you include an amount in the `value` field. Reason example: 'TAX_EXEMPT' (partial refund of exempt tax)", "Indicates that the completed transaction was refunded by the merchant. For partial refunds, we recommend that you include an amount in the `value` field. Reason example: 'TAX_EXEMPT' (partial refund of exempt tax)", "Indicates that the completed transaction was refunded by the merchant, and that this refund was reversed. For partial refunds, we recommend that you include an amount in the `value` field."], "type": "string"}, "reason": {"description": "Optional. The reason or standardized code that corresponds with this transaction event, if one exists. For example, a CHARGEBACK event with code 6005.", "type": "string"}, "value": {"description": "Optional. The value that corresponds with this transaction event, if one exists. For example, a refund event where $5.00 was refunded. Currency is obtained from the original transaction data.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1UserId": {"description": "An identifier associated with a user.", "id": "GoogleCloudRecaptchaenterpriseV1UserId", "properties": {"email": {"description": "Optional. An email address.", "type": "string"}, "phoneNumber": {"description": "Optional. A phone number. Should use the E.164 format.", "type": "string"}, "username": {"description": "Optional. A unique username, if different from all the other identifiers and `account_id` that are provided. Can be a unique login handle or display name for a user.", "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1UserInfo": {"description": "User information associated with a request protected by reCAPTCHA Enterprise.", "id": "GoogleCloudRecaptchaenterpriseV1UserInfo", "properties": {"accountId": {"description": "Optional. For logged-in requests or login/registration requests, the unique account identifier associated with this user. You can use the username if it is stable (meaning it is the same for every request associated with the same user), or any stable user ID of your choice. Leave blank for non logged-in actions or guest checkout.", "type": "string"}, "createAccountTime": {"description": "Optional. Creation time for this account associated with this user. Leave blank for non logged-in actions, guest checkout, or when there is no account associated with the current user.", "format": "google-datetime", "type": "string"}, "userIds": {"description": "Optional. Identifiers associated with this user or request.", "items": {"$ref": "GoogleCloudRecaptchaenterpriseV1UserId"}, "type": "array"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1WafSettings": {"description": "Settings specific to keys that can be used for WAF (Web Application Firewall).", "id": "GoogleCloudRecaptchaenterpriseV1WafSettings", "properties": {"wafFeature": {"description": "Required. The WAF feature for which this key is enabled.", "enum": ["WAF_FEATURE_UNSPECIFIED", "CHALLENGE_PAGE", "SESSION_TOKEN", "ACTION_TOKEN", "EXPRESS"], "enumDeprecated": [false, false, false, false, true], "enumDescriptions": ["Undefined feature.", "Redirects suspicious traffic to reCAPTCHA.", "Use reCAPTCHA session-tokens to protect the whole user session on the site's domain.", "Use reCAPTCHA action-tokens to protect user actions.", "Deprecated: Use `express_settings` instead."], "type": "string"}, "wafService": {"description": "Required. The WAF service that uses this key.", "enum": ["WAF_SERVICE_UNSPECIFIED", "CA", "FASTLY", "CLOUDFLARE", "AKAMAI"], "enumDescriptions": ["Undefined WAF", "Cloud Armor", "Fastly", "Cloudflare", "<PERSON><PERSON><PERSON><PERSON>"], "type": "string"}}, "type": "object"}, "GoogleCloudRecaptchaenterpriseV1WebKeySettings": {"description": "Settings specific to keys that can be used by websites.", "id": "GoogleCloudRecaptchaenterpriseV1WebKeySettings", "properties": {"allowAllDomains": {"description": "Optional. If set to true, it means allowed_domains are not enforced.", "type": "boolean"}, "allowAmpTraffic": {"description": "Optional. If set to true, the key can be used on AMP (Accelerated Mobile Pages) websites. This is supported only for the SCORE integration type.", "type": "boolean"}, "allowedDomains": {"description": "Optional. Domains or subdomains of websites allowed to use the key. All subdomains of an allowed domain are automatically allowed. A valid domain requires a host and must not include any path, port, query or fragment. Examples: 'example.com' or 'subdomain.example.com'", "items": {"type": "string"}, "type": "array"}, "challengeSecurityPreference": {"description": "Optional. Settings for the frequency and difficulty at which this key triggers captcha challenges. This should only be specified for IntegrationTypes CHECKBOX and INVISIBLE and SCORE_AND_CHALLENGE.", "enum": ["CHALLENGE_SECURITY_PREFERENCE_UNSPECIFIED", "USABILITY", "BALANCE", "SECURITY"], "enumDescriptions": ["Default type that indicates this enum hasn't been specified.", "Key tends to show fewer and easier challenges.", "Key tends to show balanced (in amount and difficulty) challenges.", "Key tends to show more and harder challenges."], "type": "string"}, "integrationType": {"description": "Required. Describes how this key is integrated with the website.", "enum": ["INTEGRATION_TYPE_UNSPECIFIED", "SCORE", "CHECKBOX", "INVISIBLE"], "enumDescriptions": ["Default type that indicates this enum hasn't been specified. This is not a valid IntegrationType, one of the other types must be specified instead.", "Only used to produce scores. It doesn't display the \"I'm not a robot\" checkbox and never shows captcha challenges.", "Displays the \"I'm not a robot\" checkbox and may show cap<PERSON>a challenges after it is checked.", "Doesn't display the \"I'm not a robot\" checkbox, but may show captcha challenges after risk analysis."], "type": "string"}}, "type": "object"}, "GoogleProtobufEmpty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "GoogleProtobufEmpty", "properties": {}, "type": "object"}, "GoogleRpcStatus": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "GoogleRpcStatus", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "reCAPTCHA Enterprise API", "version": "v1", "version_module": true}