import sys
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ._zaxis import ZaxisValidator
    from ._yaxis import Ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
    from ._xaxis import <PERSON>axisValidator
    from ._uirevision import UirevisionValidator
    from ._hovermode import <PERSON><PERSON><PERSON>deVali<PERSON><PERSON>
    from ._dragmode import <PERSON>agmodeVali<PERSON><PERSON>
    from ._domain import DomainValidator
    from ._camera import <PERSON>Valida<PERSON>
    from ._bgcolor import BgcolorValidator
    from ._aspectratio import Aspect<PERSON><PERSON>Valida<PERSON>
    from ._aspectmode import AspectmodeValidator
    from ._annotationdefaults import AnnotationdefaultsValidator
    from ._annotations import AnnotationsValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._zaxis.ZaxisValidator",
            "._yaxis.YaxisValidator",
            "._xaxis.XaxisValidator",
            "._uirevision.UirevisionValidator",
            "._hovermode.HovermodeValidator",
            "._dragmode.DragmodeValidator",
            "._domain.DomainValidator",
            "._camera.CameraValidator",
            "._bgcolor.BgcolorValidator",
            "._aspectratio.AspectratioValidator",
            "._aspectmode.AspectmodeValidator",
            "._annotationdefaults.AnnotationdefaultsValidator",
            "._annotations.AnnotationsValidator",
        ],
    )
